import { AbstractFirestoreService } from '../data/google/firestore/AbstractFirestoreService';

// TypeScript interfaces based on FIELD_ANALYSIS.md

export interface PurchaseData {
  id: string;
  invoiceDate: Date;
  'on-the-way': boolean;
  ETA: Date;
  quantity: number;
  productCost: number;
  shippingCost: number;
  totalCost: number;
  notes: string;
  commercialInvoice?: object;
}

export interface CreatePurchaseData {
  invoiceDate: Date;
  'on-the-way': boolean;
  ETA: Date;
  quantity: number;
  productCost: number;
  shippingCost: number;
  totalCost: number;
  notes: string;
  commercialInvoice?: object;
}

export interface PurchaseItemData {
  id: string;
  qty: number;
  sku: string;
  unitPrice: number;
  price: number;
  description: string;
  model: string;
  itemNo: string;
}

export interface CreatePurchaseItemData {
  qty: number;
  sku: string;
  unitPrice: number;
  price: number;
  description: string;
  model: string;
  itemNo: string;
}

export class FilamentPurchaseService {
  private firestore: AbstractFirestoreService;
  private static readonly COLLECTION_NAME = 'filament-purchases';
  private static readonly ITEMS_SUBCOLLECTION = 'Items';

  constructor(firestore: AbstractFirestoreService) {
    this.firestore = firestore;
  }

  /**
   * Get all purchases with date conversion
   */
  async getAllPurchases(): Promise<PurchaseData[]> {
    try {
      return await this.firestore.getFilamentPurchases() as PurchaseData[];
    } catch (error) {
      console.error('Error getting all purchases:', error);
      throw new Error(`Failed to get all purchases: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get single purchase by ID
   */
  async getPurchase(id: string): Promise<PurchaseData | null> {
    try {
      if (!id) {
        throw new Error('Purchase ID is required');
      }
      return await this.firestore.get<PurchaseData>(FilamentPurchaseService.COLLECTION_NAME, id);
    } catch (error) {
      console.error(`Error getting purchase ${id}:`, error);
      throw new Error(`Failed to get purchase: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create new purchase
   */
  async createPurchase(data: CreatePurchaseData): Promise<{ success: boolean; id?: string; error?: any }> {
    try {
      if (!data) {
        throw new Error('Purchase data is required');
      }
      
      // Generate a new document ID
      const id = Date.now().toString();
      const result = await this.firestore.set(FilamentPurchaseService.COLLECTION_NAME, id, data);
      
      if (result.success) {
        return { success: true, id };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error creating purchase:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Create new purchase with specific ID
   */
  async createPurchaseWithId(id: string, data: CreatePurchaseData): Promise<{ success: boolean; error?: any }> {
    try {
      if (!id) {
        throw new Error('Purchase ID is required');
      }
      if (!data) {
        throw new Error('Purchase data is required');
      }
      
      const result = await this.firestore.set(FilamentPurchaseService.COLLECTION_NAME, id, data);
      
      if (result.success) {
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error(`Error creating purchase with ID ${id}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Update purchase
   */
  async updatePurchase(id: string, data: Partial<PurchaseData>): Promise<void> {
    try {
      if (!id) {
        throw new Error('Purchase ID is required');
      }
      if (!data) {
        throw new Error('Update data is required');
      }
      
      // Remove id from update data if present
      const { id: _, ...updateData } = data;
      
      await this.firestore.updateFilamentPurchase(id, updateData);
    } catch (error) {
      console.error(`Error updating purchase ${id}:`, error);
      throw new Error(`Failed to update purchase: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Delete purchase
   */
  async deletePurchase(id: string): Promise<void> {
    try {
      if (!id) {
        throw new Error('Purchase ID is required');
      }
      await this.firestore.deleteDoc(FilamentPurchaseService.COLLECTION_NAME, id);
    } catch (error) {
      console.error(`Error deleting purchase ${id}:`, error);
      throw new Error(`Failed to delete purchase: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get all items for a purchase
   */
  async getPurchaseItems(purchaseId: string): Promise<PurchaseItemData[]> {
    try {
      if (!purchaseId) {
        throw new Error('Purchase ID is required');
      }
      return await this.firestore.getFilamentPurchaseItems(purchaseId) as PurchaseItemData[];
    } catch (error) {
      console.error(`Error getting items for purchase ${purchaseId}:`, error);
      throw new Error(`Failed to get purchase items: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Create new purchase item
   */
  async createPurchaseItem(purchaseId: string, data: CreatePurchaseItemData): Promise<string> {
    try {
      if (!purchaseId) {
        throw new Error('Purchase ID is required');
      }
      if (!data) {
        throw new Error('Item data is required');
      }
      
      // Generate a new item ID
      const itemId = Date.now().toString();
      
      const result = await this.firestore.setSubData(
        FilamentPurchaseService.COLLECTION_NAME,
        FilamentPurchaseService.ITEMS_SUBCOLLECTION,
        purchaseId,
        data,
        itemId
      );
      
      return result;
    } catch (error) {
      console.error(`Error creating item for purchase ${purchaseId}:`, error);
      throw new Error(`Failed to create purchase item: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Update purchase item
   */
  async updatePurchaseItem(purchaseId: string, itemId: string, data: Partial<PurchaseItemData>): Promise<void> {
    try {
      if (!purchaseId) {
        throw new Error('Purchase ID is required');
      }
      if (!itemId) {
        throw new Error('Item ID is required');
      }
      if (!data) {
        throw new Error('Update data is required');
      }
      
      // Remove id from update data if present
      const { id: _, ...updateData } = data;
      
      await this.firestore.updateFilamentPurchaseItem(purchaseId, itemId, updateData);
    } catch (error) {
      console.error(`Error updating item ${itemId} for purchase ${purchaseId}:`, error);
      throw new Error(`Failed to update purchase item: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Delete purchase item
   */
  async deletePurchaseItem(purchaseId: string, itemId: string): Promise<void> {
    try {
      if (!purchaseId) {
        throw new Error('Purchase ID is required');
      }
      if (!itemId) {
        throw new Error('Item ID is required');
      }
      
      const fullPath = `${FilamentPurchaseService.COLLECTION_NAME}/${purchaseId}/${FilamentPurchaseService.ITEMS_SUBCOLLECTION}`;
      await this.firestore.deleteDoc(fullPath, itemId);
    } catch (error) {
      console.error(`Error deleting item ${itemId} for purchase ${purchaseId}:`, error);
      throw new Error(`Failed to delete purchase item: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get purchases where on-the-way is true
   */
  async getPurchasesOnTheWay(): Promise<PurchaseData[]> {
    try {
      return await this.firestore.getFilamentPurchasesOnTheWay() as PurchaseData[];
    } catch (error) {
      console.error('Error getting purchases on the way:', error);
      throw new Error(`Failed to get purchases on the way: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get purchase with its items included
   */
  async getPurchaseWithItems(purchaseId: string): Promise<(PurchaseData & { items: PurchaseItemData[] }) | null> {
    try {
      if (!purchaseId) {
        throw new Error('Purchase ID is required');
      }
      
      const purchase = await this.getPurchase(purchaseId);
      if (!purchase) {
        return null;
      }
      
      const items = await this.getPurchaseItems(purchaseId);
      
      return {
        ...purchase,
        items
      };
    } catch (error) {
      console.error(`Error getting purchase with items ${purchaseId}:`, error);
      throw new Error(`Failed to get purchase with items: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get all purchases with their items included
   */
  async getAllPurchasesWithItems(): Promise<(PurchaseData & { items: PurchaseItemData[] })[]> {
    try {
      const purchases = await this.getAllPurchases();
      const purchasesWithItems = await Promise.all(
        purchases.map(async (purchase) => {
          const items = await this.getPurchaseItems(purchase.id);
          return {
            ...purchase,
            items
          };
        })
      );
      
      return purchasesWithItems;
    } catch (error) {
      console.error('Error getting all purchases with items:', error);
      throw new Error(`Failed to get all purchases with items: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Calculate purchase totals (helper method)
   */
  calculatePurchaseTotals(items: PurchaseItemData[]): {
    totalQuantity: number;
    totalItemCost: number;
    averageUnitPrice: number;
  } {
    const totalQuantity = items.reduce((sum, item) => sum + item.qty, 0);
    const totalItemCost = items.reduce((sum, item) => sum + item.price, 0);
    const averageUnitPrice = totalQuantity > 0 ? totalItemCost / totalQuantity : 0;
    
    return {
      totalQuantity,
      totalItemCost,
      averageUnitPrice: Math.round(averageUnitPrice * 100) / 100
    };
  }
}

export default FilamentPurchaseService;
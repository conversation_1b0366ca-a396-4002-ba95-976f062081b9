"use client";
import { useState } from 'react';
import { 
  searchUserByEmail,
  changeUserEmail,
  cancelStripeSubscription,
  setUserAdmin,
  type UserSearchResult,
  type ChangeEmailResult,
  type CancelSubscriptionResult,
  type SetAdminResult,
  type FirebaseUser 
} from './actions';
import LoadingButton from '~/components/loadingButton';
import { toast } from 'sonner';
import { Checkbox } from '~/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Label } from "~/components/ui/label";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "~/components/ui/alert-dialog";
import { Button } from '~/components/ui/button';
import { ChevronDownIcon, ChevronRightIcon } from "@radix-ui/react-icons";

// Helper function to format timestamp
const formatTimestamp = (timestamp: number | undefined | null): string => {
  if (!timestamp) return 'N/A';
  return new Date(timestamp * 1000).toLocaleDateString();
};

// Helper function to format date
const formatDate = (date: Date | undefined | null): string => {
  if (!date) return 'N/A';
  return date.toLocaleDateString();
};

export default function UserSearch() {
  // Search state
  const [searchEmail, setSearchEmail] = useState('');
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<UserSearchResult | null>(null);
  
  // Email change state
  const [currentEmail, setCurrentEmail] = useState('');
  const [newEmail, setNewEmail] = useState('');
  const [changingEmail, setChangingEmail] = useState(false);

  // Subscription cancellation state
  const [cancellingSubscriptionId, setCancellingSubscriptionId] = useState<string | null>(null);
  const [isCancelConfirmOpen, setIsCancelConfirmOpen] = useState(false);
  const [cancelWithRefund, setCancelWithRefund] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);
  
  // Admin setting state
  const [settingAdminUserId, setSettingAdminUserId] = useState<string | null>(null);

  // Advanced data panel state
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);

  async function handleSearchEmail() {
    if (!searchEmail) {
      toast.error('Please enter an email address');
      return;
    }

    setSearchLoading(true);
    setSearchResult(null);

    try {
      const result = await searchUserByEmail(searchEmail);
      setSearchResult(result);
      toast.success('Search completed');
      // Pre-fill the current email for the email change form
      setCurrentEmail(searchEmail);
    } catch (error: any) {
      console.error('Error searching for email:', error);
      toast.error(`Error searching for email: ${error.message}`);
    } finally {
      setSearchLoading(false);
    }
  }

  async function handleChangeEmail() {
    if (!currentEmail || !newEmail) {
      toast.error('Please enter both current and new email addresses');
      return;
    }

    setChangingEmail(true);

    try {
      const result = await changeUserEmail(currentEmail, newEmail);
      
      if (result.success) {
        toast.success(result.message);
        // Update the UI with the new email details
        setSearchEmail(newEmail);
        await handleSearchEmail();
      } else {
        toast.error(result.message);
        console.error('Errors:', result.errors);
      }
    } catch (error: any) {
      console.error('Error changing email:', error);
      toast.error(`Error changing email: ${error.message}`);
    } finally {
      setChangingEmail(false);
    }
  }

  // Handler for initiating subscription cancellation
  async function handleCancelSubscription() {
    if (!cancellingSubscriptionId) return;

    setCancelLoading(true);
    setIsCancelConfirmOpen(false);

    try {
      const result = await cancelStripeSubscription(cancellingSubscriptionId, cancelWithRefund);
      if (result.success) {
        toast.success(result.message);
        // Refresh search results to show updated subscription status
        await handleSearchEmail();
      } else {
        toast.error(result.message);
        console.error("Cancellation error:", result.error);
      }
    } catch (error: any) {
      console.error('Error cancelling subscription:', error);
      toast.error(`Error cancelling subscription: ${error.message}`);
    } finally {
      setCancelLoading(false);
      setCancellingSubscriptionId(null);
    }
  }

  // Handler for setting a user as admin
  async function handleSetAdmin(userId: string) {
    if (!userId) return;

    setSettingAdminUserId(userId);

    try {
      const result = await setUserAdmin(userId);
      if (result.success) {
        toast.success(result.message);
        // Refresh search results to show updated claims
        await handleSearchEmail();
      } else {
        toast.error(result.message);
        console.error("Set admin error:", result.error);
      }
    } catch (error: any) {
      console.error('Error setting user as admin:', error);
      toast.error(`Error setting user as admin: ${error.message}`);
    } finally {
      setSettingAdminUserId(null);
    }
  }

  // Helper function to get user summary data
  const getUserSummary = () => {
    if (!searchResult) return null;

    const firebaseUser = searchResult.firebaseUsers[0]; // Get first Firebase user
    const auth0User = searchResult.auth0Users[0]; // Get first Auth0 user
    const subscription = searchResult.subscriptionData[0]; // Get first subscription
    const liveSubscription = searchResult.liveStripeSubscriptions[0]; // Get first live subscription

    // Get login providers
    const loginProviders = firebaseUser?.providerData?.map(p => p.providerId).join(', ') || 'N/A';

    return {
      email: firebaseUser?.email || searchEmail,
      created: formatDate(firebaseUser?.createdAt),
      lastLogin: formatDate(firebaseUser?.lastLoginAt),
      loginProviders,
      // Cookiecad Subscription Record
      subscriptionEmail: subscription?.email || 'N/A',
      subscriptionPeriodEndDate: subscription?.subscriptionPeriodEndDate || 'N/A',
      subscriptionStatus: subscription?.subscriptionStatus || 'N/A',
      subscriptionPlan: subscription?.subscriptionPlan || 'N/A',
      // Stripe Subscription
      stripeStatus: liveSubscription?.status || 'N/A',
      stripePlan: getStripePlanDisplay(liveSubscription),
      stripeCurrentPeriodEnd: formatTimestamp(liveSubscription?.current_period_end),
      stripeCancelsAtPeriodEnd: liveSubscription?.cancel_at_period_end && liveSubscription?.cancel_at 
        ? `Yes (${formatTimestamp(liveSubscription.cancel_at)})` 
        : 'No'
    };
  };

  // Helper function to get Stripe plan display
  const getStripePlanDisplay = (subscription: any) => {
    if (!subscription?.items?.data?.length) return 'N/A';
    
    const planItem = subscription.items.data[0];
    if (!planItem?.plan) return 'N/A';
    
    const plan = planItem.plan;
    if (plan.nickname && plan.nickname.toLowerCase().includes('name your price')) {
      return 'Name Your Price';
    }
    
    if (plan.interval) {
      switch (plan.interval) {
        case 'month':
          return plan.interval_count === 1 ? 'Monthly' : `Every ${plan.interval_count} Months`;
        case 'year':
          return plan.interval_count === 1 ? 'Annual' : `Every ${plan.interval_count} Years`;
        default:
          const formattedInterval = plan.interval.charAt(0).toUpperCase() + plan.interval.slice(1);
          return `${plan.interval_count > 1 ? `${plan.interval_count} ` : ''}${formattedInterval}`;
      }
    }
    
    return 'Unknown Plan';
  };

  const summary = getUserSummary();

  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="flex flex-col gap-8 w-full max-w-4xl">
        <h1 className="text-2xl font-bold">User Search</h1>
        
        <div className="space-y-8">
          {/* Email Search Section */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Search by Email</h2>
            <div className="flex gap-4">
              <input
                type="email"
                value={searchEmail}
                onChange={(e) => setSearchEmail(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSearchEmail();
                  }
                }}
                placeholder="Enter email address"
                className="flex-1 px-4 py-2 border rounded-lg"
              />
              <LoadingButton onClick={handleSearchEmail} loading={searchLoading}>
                Search User
              </LoadingButton>
            </div>

            {/* User Summary */}
            {summary && (
              <div className="border p-6 rounded-lg bg-gray-50">
                <h3 className="text-lg font-semibold mb-4">User Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p><strong>Email:</strong> {summary.email}</p>
                    <p><strong>Created:</strong> {summary.created}</p>
                    <p><strong>Last Login:</strong> {summary.lastLogin}</p>
                    <p><strong>Login Providers:</strong> {summary.loginProviders}</p>
                  </div>
                  <div>
                    <p className="font-medium mb-2">Cookiecad Subscription Record</p>
                    <p><strong>Email:</strong> {summary.subscriptionEmail}</p>
                    <p><strong>Subscription Period End Date:</strong> {summary.subscriptionPeriodEndDate}</p>
                    <p><strong>Subscription Status:</strong> {summary.subscriptionStatus}</p>
                    <p><strong>Subscription Plan:</strong> {summary.subscriptionPlan}</p>

                    <p className="font-medium mb-2 mt-4">Stripe Subscription</p>
                    <p><strong>Status:</strong> {summary.stripeStatus}</p>
                    <p><strong>Plan:</strong> {summary.stripePlan}</p>
                    <p><strong>Current Period End:</strong> {summary.stripeCurrentPeriodEnd}</p>
                    <p><strong>Cancels At Period End:</strong> {summary.stripeCancelsAtPeriodEnd}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Advanced User Data Panel */}
            {searchResult && (
              <div>
                <Button
                  variant="outline"
                  className="w-full justify-between"
                  onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
                >
                  Advanced User Data
                  {isAdvancedOpen ? <ChevronDownIcon className="h-4 w-4" /> : <ChevronRightIcon className="h-4 w-4" />}
                </Button>
                {isAdvancedOpen && (
                  <div className="space-y-6 mt-4">
                  {/* Display any errors from the search */}
                  {searchResult.errors && searchResult.errors.length > 0 && (
                    <Alert variant="destructive">
                      <ExclamationTriangleIcon className="h-4 w-4" />
                      <AlertTitle>Search Errors</AlertTitle>
                      <AlertDescription>
                        <ul>
                          {searchResult.errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Firebase Auth Section */}
                  {searchResult.firebaseUsers.length > 0 && (
                    <div className="border p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Firebase Auth ({searchResult.firebaseUsers.length} users)</h3>
                      <div className="space-y-6">
                        {searchResult.firebaseUsers.map((user: FirebaseUser) => (
                          <div key={user.id} className="border-l-4 border-blue-500 pl-4 py-2 space-y-3">
                            <div className="space-y-2">
                              <div>ID: {user.id}</div>
                              <div>Email: {user.email}</div>
                              {user.createdAt && <div>Created: {user.createdAt.toLocaleDateString()}</div>}
                              {user.lastLoginAt && <div>Last Login: {user.lastLoginAt.toLocaleDateString()}</div>}

                              {user.providerData && user.providerData.length > 0 && (
                                <div className="mt-2">
                                  <h4 className="font-medium mb-1">Login Providers</h4>
                                  {user.providerData.map((provider: { providerId: string; uid: string }, index: number) => (
                                    <div key={index} className="ml-4">
                                      <div>Provider: {provider.providerId}</div>
                                      <div>Provider ID: {provider.uid}</div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>

                            {/* Display Custom Claims */}
                            {user.customClaims && (
                              <div className="mt-3">
                                <h4 className="font-medium mb-1">Custom Claims</h4>
                                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                                  {JSON.stringify(user.customClaims, null, 2)}
                                </pre>
                              </div>
                            )}

                            {/* Set Admin Button */}
                            <div className="mt-3">
                              <LoadingButton
                                size="sm"
                                onClick={() => handleSetAdmin(user.id)}
                                loading={settingAdminUserId === user.id}
                                disabled={settingAdminUserId === user.id || user.customClaims?.cookiecadPermissions?.isAdmin === true}
                                className={user.customClaims?.cookiecadPermissions?.isAdmin === true ? "bg-gray-400 cursor-not-allowed" : "bg-purple-600 hover:bg-purple-700"}
                              >
                                {settingAdminUserId === user.id
                                  ? 'Setting...'
                                  : user.customClaims?.cookiecadPermissions?.isAdmin === true
                                  ? 'Already Admin'
                                  : 'Set as Admin'}
                              </LoadingButton>
                            </div>

                            {/* Display Subscriptions */}
                            {(() => {
                              console.log('Checking subscriptions for user:', user.id);
                              console.log('All subscription data:', searchResult.subscriptionData);
                              const userSubs = searchResult.subscriptionData.filter(sub => sub.id === user.id);
                              console.log('Filtered subscriptions:', userSubs);
                              return userSubs.length > 0 ? (
                                <div className="mt-4">
                                  <h4 className="font-medium mb-2">Subscriptions</h4>
                                  {userSubs.map((sub) => (
                                    <pre key={sub.id} className="bg-gray-100 p-2 rounded text-sm overflow-auto">
                                      {JSON.stringify(sub, null, 2)}
                                    </pre>
                                  ))}
                                </div>
                              ) : null;
                            })()}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Live Stripe Subscriptions */}
                  {searchResult.liveStripeSubscriptions && searchResult.liveStripeSubscriptions.length > 0 && (
                    <div className="border p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Live Stripe Subscriptions</h3>
                      {searchResult.liveStripeSubscriptions.map((sub) => (
                        <div key={sub.id} className="bg-gray-50 p-3 rounded border border-gray-200 mb-3 text-sm">
                          <p><strong>Subscription ID:</strong> {sub.id}</p>
                          <p><strong>Status:</strong> <span className={`font-semibold ${sub.status === 'active' ? 'text-green-600' : sub.status === 'canceled' ? 'text-red-600' : 'text-yellow-600'}`}>{sub.status}</span></p>
                          <p><strong>Plan:</strong> {getStripePlanDisplay(sub)}</p>
                          <p><strong>Current Period End:</strong> {formatTimestamp(sub.current_period_end)}</p>
                          {sub.cancel_at_period_end && sub.cancel_at && (
                            <p className="text-orange-600"><strong>Cancels At Period End:</strong> Yes ({formatTimestamp(sub.cancel_at)})</p>
                          )}
                          {!sub.cancel_at_period_end && sub.cancel_at && (
                            <p className="text-red-600"><strong>Cancelled At:</strong> {formatTimestamp(sub.cancel_at)}</p>
                          )}

                          {/* Cancel Button and Dialog - Show for ANY active subscription */}
                          {sub.status === 'active' && (
                            <AlertDialog open={isCancelConfirmOpen && cancellingSubscriptionId === sub.id} onOpenChange={(open) => { if (!open) { setIsCancelConfirmOpen(false); setCancellingSubscriptionId(null); } }}>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  className="mt-2"
                                  onClick={() => {
                                    setCancellingSubscriptionId(sub.id);
                                    setIsCancelConfirmOpen(true);
                                    setCancelWithRefund(false);
                                  }}
                                  disabled={cancelLoading && cancellingSubscriptionId === sub.id}
                                >
                                  {cancelLoading && cancellingSubscriptionId === sub.id ? 'Cancelling...' : 'Cancel Plan'}
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Cancel Subscription?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Subscription ID: {sub.id} <br />
                                    {sub.cancel_at_period_end && sub.cancel_at && (
                                       <span className="text-orange-600">This subscription is already set to cancel on {formatTimestamp(sub.cancel_at)}.</span>
                                    )}
                                    <br/>Choose how to proceed:
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <div className="py-4 space-y-2">
                                   <RadioGroup
                                     value={cancelWithRefund ? 'immediate' : 'period_end'}
                                     onValueChange={(value) => setCancelWithRefund(value === 'immediate')}
                                     className="space-y-2"
                                   >
                                       <div className="flex items-center space-x-2">
                                           <RadioGroupItem value="period_end" id={`cancel-period-end-${sub.id}`} disabled={sub.cancel_at_period_end}/>
                                           <Label htmlFor={`cancel-period-end-${sub.id}`} className={sub.cancel_at_period_end ? 'text-muted-foreground' : ''}>
                                               {sub.cancel_at_period_end ? `Already set to cancel at period end (${formatTimestamp(sub.current_period_end)})` : `Cancel at end of current period (${formatTimestamp(sub.current_period_end)})`}
                                           </Label>
                                       </div>
                                       <div className="flex items-center space-x-2">
                                           <RadioGroupItem value="immediate" id={`cancel-immediate-${sub.id}`} />
                                           <Label htmlFor={`cancel-immediate-${sub.id}`}>Cancel immediately (No refund processed automatically)</Label>
                                       </div>
                                   </RadioGroup>
                                   <p className="text-xs text-muted-foreground pt-2">
                                       Note: Immediate cancellation deletes the subscription in Stripe. Refunds are not automatically processed by this tool. Selecting &apos;Cancel at period end&apos; only updates the setting in Stripe if not already set.
                                   </p>
                                </div>
                                <AlertDialogFooter>
                                  <AlertDialogCancel onClick={() => { setIsCancelConfirmOpen(false); setCancellingSubscriptionId(null); }}>Back</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={handleCancelSubscription}
                                    disabled={cancelLoading || (cancelWithRefund === false && sub.cancel_at_period_end)}
                                   >
                                    Confirm Cancellation
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Auth0 Section */}
                  {searchResult.auth0Users.length > 0 && (
                    <div className="border p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Auth0</h3>
                      {searchResult.auth0Users.map((user) => (
                        <div key={user.user_id} className="space-y-4">
                          <div>
                            <div>ID: {user.user_id}</div>
                            <div>Email: {user.email}</div>
                            <div>Name: {user.name}</div>
                            <div>Email Verified: {user.email_verified ? 'Yes' : 'No'}</div>
                            <div>Last Login: {new Date(user.last_login).toLocaleDateString()}</div>
                            <div>Login Count: {user.logins_count}</div>
                          </div>

                          {user.app_metadata && (
                            <div>
                              <h4 className="font-medium mb-2">App Metadata</h4>
                              <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
                                {JSON.stringify(user.app_metadata, null, 2)}
                              </pre>
                            </div>
                          )}

                          {user.identities && user.identities.length > 0 && (
                            <div>
                              <h4 className="font-medium mb-2">Linked Accounts</h4>
                              {user.identities.map((identity: any) => (
                                <div key={identity.user_id} className="ml-4">
                                  <div>Provider: {identity.provider}</div>
                                  <div>ID: {identity.user_id}</div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Stripe Customer Information */}
                  {searchResult.stripeCustomers.length > 0 && (
                    <div className="border p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Stripe Customers</h3>

                      {/* Linked Stripe Customers */}
                      {searchResult.stripeCustomers.filter(customer => customer.isLinked).length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium mb-2">Linked Stripe Customers</h4>
                          <div className="space-y-4">
                            {searchResult.stripeCustomers
                              .filter(customer => customer.isLinked)
                              .map(customer => (
                                <div key={customer.id} className="border-l-4 border-green-500 pl-4 py-2">
                                  <div className="space-y-1">
                                    <div>ID: {customer.id}</div>
                                    <div>Email: {customer.email}</div>
                                    {customer.name && <div>Name: {customer.name}</div>}
                                    {customer.phone && <div>Phone: {customer.phone}</div>}
                                    <div>Created: {new Date(customer.created * 1000).toLocaleDateString()}</div>

                                    {customer.metadata && Object.keys(customer.metadata).length > 0 && (
                                      <div className="mt-2">
                                        <h5 className="font-medium mb-1">Metadata</h5>
                                        <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                                          {JSON.stringify(customer.metadata, null, 2)}
                                        </pre>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}

                      {/* Unlinked Stripe Customers */}
                      {searchResult.stripeCustomers.filter(customer => !customer.isLinked).length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2 text-orange-600 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            Unlinked Stripe Accounts Found!
                          </h4>
                          <div className="bg-orange-50 border border-orange-200 rounded-md p-3 mb-3">
                            <p className="text-sm text-orange-700">
                              These Stripe accounts have the same email but are not linked to any Auth0 or Firestore records.
                              They may need to be linked or represent orphaned accounts.
                            </p>
                          </div>
                          <div className="space-y-4">
                            {searchResult.stripeCustomers
                              .filter(customer => !customer.isLinked)
                              .map(customer => (
                                <div key={customer.id} className="border-l-4 border-orange-500 pl-4 py-2">
                                  <div className="space-y-1">
                                    <div>ID: {customer.id}</div>
                                    <div>Email: {customer.email}</div>
                                    {customer.name && <div>Name: {customer.name}</div>}
                                    {customer.phone && <div>Phone: {customer.phone}</div>}
                                    <div>Created: {new Date(customer.created * 1000).toLocaleDateString()}</div>

                                    {customer.metadata && Object.keys(customer.metadata).length > 0 && (
                                      <div className="mt-2">
                                        <h5 className="font-medium mb-1">Metadata</h5>
                                        <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                                          {JSON.stringify(customer.metadata, null, 2)}
                                        </pre>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  </div>
                )}
              </div>
            )}

            {/* Email Change Section */}
            {searchResult && (
              <div className="border p-4 rounded-lg mt-6">
                <h3 className="text-lg font-semibold mb-4">Change Email Address</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="currentEmail" className="block text-sm font-medium text-gray-700 mb-1">
                        Current Email
                      </label>
                      <input
                        id="currentEmail"
                        type="email"
                        value={currentEmail}
                        onChange={(e) => setCurrentEmail(e.target.value)}
                        className="w-full px-4 py-2 border rounded-lg"
                        disabled={changingEmail}
                      />
                    </div>
                    <div>
                      <label htmlFor="newEmail" className="block text-sm font-medium text-gray-700 mb-1">
                        New Email
                      </label>
                      <input
                        id="newEmail"
                        type="email"
                        value={newEmail}
                        onChange={(e) => setNewEmail(e.target.value)}
                        className="w-full px-4 py-2 border rounded-lg"
                        disabled={changingEmail}
                      />
                    </div>
                  </div>
                  <div className="mt-2">
                    <LoadingButton
                      onClick={handleChangeEmail}
                      loading={changingEmail}
                      className="bg-orange-500 hover:bg-orange-600"
                    >
                      Update Email Address
                    </LoadingButton>
                  </div>
                  <div className="text-sm text-gray-600 mt-2">
                    <p>This will update the email address across multiple systems:</p>
                    <ul className="list-disc list-inside ml-2">
                      <li>Auth0 user accounts</li>
                      <li>Firebase Auth</li>
                      <li>Subscription records in Firestore</li>
                      <li>Stripe customer accounts (if found)</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}

"use client";
import { useState } from 'react';
import {
  searchUserByEmail,
  changeUserEmail,
  cancelStripeSubscription,
  setUserAdmin,
  getCustomerPayments,
  refundPayment,
  type UserSearchResult,
  type ChangeEmailResult,
  type CancelSubscriptionResult,
  type SetAdminResult,
  type CustomerPaymentsResult,
  type RefundResult,
  type FirebaseUser
} from './actions';
import LoadingButton from '~/components/loadingButton';
import { toast } from 'sonner';
import { Checkbox } from '~/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Label } from "~/components/ui/label";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "~/components/ui/alert-dialog";
import { Button } from '~/components/ui/button';
import { ChevronDownIcon, ChevronRightIcon } from "@radix-ui/react-icons";

// Helper function to format timestamp
const formatTimestamp = (timestamp: number | undefined | null): string => {
  if (!timestamp) return 'N/A';
  return new Date(timestamp * 1000).toLocaleDateString();
};

// Helper function to format date
const formatDate = (date: Date | undefined | null): string => {
  if (!date) return 'N/A';
  return date.toLocaleDateString();
};

export default function UserSearch() {
  // Search state
  const [searchEmail, setSearchEmail] = useState('');
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<UserSearchResult | null>(null);
  
  // Email change state
  const [currentEmail, setCurrentEmail] = useState('');
  const [newEmail, setNewEmail] = useState('');
  const [changingEmail, setChangingEmail] = useState(false);

  // Subscription cancellation state
  const [cancellingSubscriptionId, setCancellingSubscriptionId] = useState<string | null>(null);
  const [isCancelConfirmOpen, setIsCancelConfirmOpen] = useState(false);
  const [cancelWithRefund, setCancelWithRefund] = useState(false);
  const [cancelLoading, setCancelLoading] = useState(false);
  const [cancelAndRefundLastPayment, setCancelAndRefundLastPayment] = useState(false);
  const [lastPaymentData, setLastPaymentData] = useState<any>(null);
  
  // Admin setting state
  const [settingAdminUserId, setSettingAdminUserId] = useState<string | null>(null);

  // Advanced data panel state
  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false);

  // Customer payments state
  const [isPaymentsOpen, setIsPaymentsOpen] = useState(false);
  const [paymentsLoading, setPaymentsLoading] = useState(false);
  const [paymentsResult, setPaymentsResult] = useState<CustomerPaymentsResult | null>(null);

  // Refund state
  const [refundingPaymentId, setRefundingPaymentId] = useState<string | null>(null);
  const [isRefundConfirmOpen, setIsRefundConfirmOpen] = useState(false);
  const [refundLoading, setRefundLoading] = useState(false);
  const [refundPaymentData, setRefundPaymentData] = useState<{
    id: string;
    amount: number;
    currency: string;
    created: number;
    type: 'payment' | 'charge';
  } | null>(null);

  async function handleSearchEmail() {
    if (!searchEmail) {
      toast.error('Please enter an email address');
      return;
    }

    setSearchLoading(true);
    setSearchResult(null);

    try {
      const result = await searchUserByEmail(searchEmail);
      setSearchResult(result);
      toast.success('Search completed');
      // Pre-fill the current email for the email change form
      setCurrentEmail(searchEmail);
    } catch (error: any) {
      console.error('Error searching for email:', error);
      toast.error(`Error searching for email: ${error.message}`);
    } finally {
      setSearchLoading(false);
    }
  }

  async function handleChangeEmail() {
    if (!currentEmail || !newEmail) {
      toast.error('Please enter both current and new email addresses');
      return;
    }

    setChangingEmail(true);

    try {
      const result = await changeUserEmail(currentEmail, newEmail);
      
      if (result.success) {
        toast.success(result.message);
        // Update the UI with the new email details
        setSearchEmail(newEmail);
        await handleSearchEmail();
      } else {
        toast.error(result.message);
        console.error('Errors:', result.errors);
      }
    } catch (error: any) {
      console.error('Error changing email:', error);
      toast.error(`Error changing email: ${error.message}`);
    } finally {
      setChangingEmail(false);
    }
  }

  // Handler for initiating subscription cancellation
  async function handleCancelSubscription() {
    if (!cancellingSubscriptionId) return;

    setCancelLoading(true);
    setIsCancelConfirmOpen(false);

    try {
      // First cancel the subscription
      const result = await cancelStripeSubscription(cancellingSubscriptionId, cancelWithRefund);
      if (result.success) {
        toast.success(result.message);

        // If user chose to refund last payment, do that too
        if (cancelAndRefundLastPayment && lastPaymentData) {
          try {
            const refundResult = await refundPayment(lastPaymentData.id);
            if (refundResult.success) {
              toast.success(`Subscription cancelled and last payment refunded: ${refundResult.message}`);
            } else {
              toast.warning(`Subscription cancelled but refund failed: ${refundResult.message}`);
            }
          } catch (refundError: any) {
            console.error('Error refunding last payment:', refundError);
            toast.warning(`Subscription cancelled but refund failed: ${refundError.message}`);
          }
        }

        // Refresh search results to show updated subscription status
        await handleSearchEmail();
        // Refresh payments if they were loaded
        if (paymentsResult) {
          await handleLoadPayments();
        }
      } else {
        toast.error(result.message);
        console.error("Cancellation error:", result.error);
      }
    } catch (error: any) {
      console.error('Error cancelling subscription:', error);
      toast.error(`Error cancelling subscription: ${error.message}`);
    } finally {
      setCancelLoading(false);
      setCancellingSubscriptionId(null);
      setCancelAndRefundLastPayment(false);
      setLastPaymentData(null);
    }
  }

  // Handler for setting a user as admin
  async function handleSetAdmin(userId: string) {
    if (!userId) return;

    setSettingAdminUserId(userId);

    try {
      const result = await setUserAdmin(userId);
      if (result.success) {
        toast.success(result.message);
        // Refresh search results to show updated claims
        await handleSearchEmail();
      } else {
        toast.error(result.message);
        console.error("Set admin error:", result.error);
      }
    } catch (error: any) {
      console.error('Error setting user as admin:', error);
      toast.error(`Error setting user as admin: ${error.message}`);
    } finally {
      setSettingAdminUserId(null);
    }
  }

  // Handler for loading customer payments
  async function handleLoadPayments() {
    if (!searchResult) return;

    // Get all linked Stripe customer IDs
    const customerIds = [...searchResult.linkedStripeCustomerIds];

    if (customerIds.length === 0) {
      toast.error('No linked Stripe customers found');
      return;
    }

    setPaymentsLoading(true);
    setPaymentsResult(null);

    try {
      const result = await getCustomerPayments(customerIds);
      setPaymentsResult(result);

      if (result.errors.length > 0) {
        toast.warning(`Payments loaded with ${result.errors.length} errors. Check console for details.`);
        console.error('Payment loading errors:', result.errors);
      } else {
        toast.success(`Loaded ${result.payments.length} payment intents and ${result.charges.length} charges`);
      }
    } catch (error: any) {
      console.error('Error loading payments:', error);
      toast.error(`Error loading payments: ${error.message}`);
    } finally {
      setPaymentsLoading(false);
    }
  }

  // Handler for initiating refund
  function handleInitiateRefund(paymentId: string, amount: number, currency: string, created: number, type: 'payment' | 'charge') {
    setRefundPaymentData({
      id: paymentId,
      amount,
      currency,
      created,
      type
    });
    setRefundingPaymentId(paymentId);
    setIsRefundConfirmOpen(true);
  }

  // Handler for confirming refund
  async function handleConfirmRefund() {
    if (!refundPaymentData) return;

    setRefundLoading(true);
    setIsRefundConfirmOpen(false);

    try {
      const result = await refundPayment(refundPaymentData.id);
      if (result.success) {
        toast.success(result.message);
        // Refresh payment data to show the refund
        await handleLoadPayments();
      } else {
        toast.error(result.message);
        console.error("Refund error:", result.error);
      }
    } catch (error: any) {
      console.error('Error processing refund:', error);
      toast.error(`Error processing refund: ${error.message}`);
    } finally {
      setRefundLoading(false);
      setRefundingPaymentId(null);
      setRefundPaymentData(null);
    }
  }

  // Helper function to calculate days ago
  const getDaysAgo = (timestamp: number) => {
    const now = new Date();
    const paymentDate = new Date(timestamp * 1000);
    const diffTime = Math.abs(now.getTime() - paymentDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Handler for initiating subscription cancellation from summary
  function handleInitiateCancellationFromSummary(subscription: any) {
    setCancellingSubscriptionId(subscription.id);

    // Load payments if not already loaded to check for last payment
    if (!paymentsResult) {
      handleLoadPayments().then(() => {
        const lastPayment = getLastPaymentForSubscription(subscription.id);
        setLastPaymentData(lastPayment);
        setIsCancelConfirmOpen(true);
      });
    } else {
      const lastPayment = getLastPaymentForSubscription(subscription.id);
      setLastPaymentData(lastPayment);
      setIsCancelConfirmOpen(true);
    }

    // Default to cancel at period end
    setCancelWithRefund(false);
    setCancelAndRefundLastPayment(false);
  };

  // Helper function to get user summary data
  const getUserSummary = () => {
    if (!searchResult) return null;

    const firebaseUser = searchResult.firebaseUsers[0]; // Get first Firebase user
    const auth0User = searchResult.auth0Users[0]; // Get first Auth0 user
    const subscription = searchResult.subscriptionData[0]; // Get first subscription
    const liveSubscription = searchResult.liveStripeSubscriptions[0]; // Get first live subscription

    // Get login providers
    const loginProviders = firebaseUser?.providerData?.map(p => p.providerId).join(', ') || 'N/A';

    return {
      email: firebaseUser?.email || searchEmail,
      created: formatDate(firebaseUser?.createdAt),
      lastLogin: formatDate(firebaseUser?.lastLoginAt),
      loginProviders,
      // Cookiecad Subscription Record
      subscriptionEmail: subscription?.email || 'N/A',
      subscriptionPeriodEndDate: subscription?.subscriptionPeriodEndDate || 'N/A',
      subscriptionStatus: subscription?.subscriptionStatus || 'N/A',
      subscriptionPlan: subscription?.subscriptionPlan || 'N/A',
      // Stripe Subscription
      stripeStatus: liveSubscription?.status || 'N/A',
      stripePlan: getStripePlanDisplay(liveSubscription),
      stripeCurrentPeriodEnd: formatTimestamp(liveSubscription?.current_period_end),
      stripeCancelsAtPeriodEnd: liveSubscription?.cancel_at_period_end && liveSubscription?.cancel_at
        ? `Yes (${formatTimestamp(liveSubscription.cancel_at)})`
        : 'No',
      // For subscription management
      liveSubscription: liveSubscription
    };
  };

  // Helper function to get the last payment for a subscription
  const getLastPaymentForSubscription = (subscriptionId: string) => {
    if (!paymentsResult) return null;

    console.log('Looking for payments for subscription:', subscriptionId);
    console.log('Available payments:', paymentsResult.payments.length);
    console.log('Available charges:', paymentsResult.charges.length);

    // Look through both payments and charges to find the most recent one for this subscription
    const allPayments = [
      ...paymentsResult.payments.map(p => ({ ...p, type: 'payment' as const })),
      ...paymentsResult.charges.map(c => ({ ...c, type: 'charge' as const }))
    ];

    console.log('All payments:', allPayments.map(p => ({ id: p.id, invoice: p.invoice, type: p.type })));

    // Filter payments related to this subscription and sort by date (newest first)
    const subscriptionPayments = allPayments
      .filter(payment => {
        // Check if payment is related to this subscription
        let isRelated = false;
        if (payment.type === 'payment') {
          isRelated = payment.invoice &&
                     typeof payment.invoice === 'object' &&
                     payment.invoice.subscription === subscriptionId;
        } else {
          isRelated = payment.invoice &&
                     typeof payment.invoice === 'object' &&
                     payment.invoice.subscription === subscriptionId;
        }

        // Also check if invoice is a string ID and try to match
        if (!isRelated && typeof payment.invoice === 'string') {
          // For now, we'll include all payments if we can't determine subscription relationship
          // This is a fallback - in a real scenario you'd want to fetch invoice details
          console.log('Payment has string invoice ID:', payment.invoice);
        }

        console.log(`Payment ${payment.id} related to subscription ${subscriptionId}:`, isRelated);
        return isRelated;
      })
      .sort((a, b) => b.created - a.created);

    console.log('Filtered subscription payments:', subscriptionPayments);

    // If no subscription-specific payments found, return the most recent payment as fallback
    if (subscriptionPayments.length === 0 && allPayments.length > 0) {
      console.log('No subscription-specific payments found, using most recent payment as fallback');
      return allPayments[0];
    }

    return subscriptionPayments[0] || null;
  };

  // Helper function to get Stripe plan display
  const getStripePlanDisplay = (subscription: any) => {
    if (!subscription?.items?.data?.length) return 'N/A';

    const planItem = subscription.items.data[0];
    if (!planItem?.plan) return 'N/A';

    const plan = planItem.plan;
    if (plan.nickname && plan.nickname.toLowerCase().includes('name your price')) {
      return 'Name Your Price';
    }

    if (plan.interval) {
      switch (plan.interval) {
        case 'month':
          return plan.interval_count === 1 ? 'Monthly' : `Every ${plan.interval_count} Months`;
        case 'year':
          return plan.interval_count === 1 ? 'Annual' : `Every ${plan.interval_count} Years`;
        default:
          const formattedInterval = plan.interval.charAt(0).toUpperCase() + plan.interval.slice(1);
          return `${plan.interval_count > 1 ? `${plan.interval_count} ` : ''}${formattedInterval}`;
      }
    }

    return 'Unknown Plan';
  };

  // Helper function to format currency amount
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100); // Stripe amounts are in cents
  };

  // Helper function to get payment status color
  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'succeeded':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'pending':
        return 'text-yellow-600';
      case 'canceled':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const summary = getUserSummary();

  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="flex flex-col gap-8 w-full max-w-4xl">
        <h1 className="text-2xl font-bold">User Search</h1>
        
        <div className="space-y-8">
          {/* Email Search Section */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Search by Email</h2>
            <div className="flex gap-4">
              <input
                type="email"
                value={searchEmail}
                onChange={(e) => setSearchEmail(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSearchEmail();
                  }
                }}
                placeholder="Enter email address"
                className="flex-1 px-4 py-2 border rounded-lg"
              />
              <LoadingButton onClick={handleSearchEmail} loading={searchLoading}>
                Search User
              </LoadingButton>
            </div>

            {/* User Summary */}
            {summary && (
              <div className="border p-6 rounded-lg bg-gray-50">
                <h3 className="text-lg font-semibold mb-4">User Summary</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p><strong>Email:</strong> {summary.email}</p>
                    <p><strong>Created:</strong> {summary.created}</p>
                    <p><strong>Last Login:</strong> {summary.lastLogin}</p>
                    <p><strong>Login Providers:</strong> {summary.loginProviders}</p>
                  </div>
                  <div>
                    <p className="font-medium mb-2">Cookiecad Subscription Record</p>
                    <p><strong>Email:</strong> {summary.subscriptionEmail}</p>
                    <p><strong>Subscription Period End Date:</strong> {summary.subscriptionPeriodEndDate}</p>
                    <p><strong>Subscription Status:</strong> {summary.subscriptionStatus}</p>
                    <p><strong>Subscription Plan:</strong> {summary.subscriptionPlan}</p>

                    <p className="font-medium mb-2 mt-4">Stripe Subscription</p>
                    <p><strong>Status:</strong> {summary.stripeStatus}</p>
                    <p><strong>Plan:</strong> {summary.stripePlan}</p>
                    <p><strong>Current Period End:</strong> {summary.stripeCurrentPeriodEnd}</p>
                    <p><strong>Cancels At Period End:</strong> {summary.stripeCancelsAtPeriodEnd}</p>
                  </div>
                </div>

                {/* Cancel Subscription Button */}
                {summary.liveSubscription && summary.liveSubscription.status === 'active' && (
                  <div className="mt-4 pt-4 border-t border-gray-300">
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleInitiateCancellationFromSummary(summary.liveSubscription)}
                      disabled={cancelLoading}
                    >
                      {cancelLoading ? 'Processing...' : 'Cancel Subscription'}
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Customer Payments Section */}
            {searchResult && searchResult.linkedStripeCustomerIds.size > 0 && (
              <div>
                <Button
                  variant="outline"
                  className="w-full justify-between mt-6"
                  onClick={() => {
                    setIsPaymentsOpen(!isPaymentsOpen);
                    if (!isPaymentsOpen && !paymentsResult) {
                      handleLoadPayments();
                    }
                  }}
                  disabled={paymentsLoading}
                >
                  Customer Payments {paymentsLoading && '(Loading...)'}
                  {isPaymentsOpen ? <ChevronDownIcon className="h-4 w-4" /> : <ChevronRightIcon className="h-4 w-4" />}
                </Button>
                {isPaymentsOpen && (
                  <div className="space-y-6 mt-4">
                    {paymentsLoading && (
                      <div className="text-center py-4">
                        <p>Loading payment data...</p>
                      </div>
                    )}

                    {paymentsResult && paymentsResult.errors.length > 0 && (
                      <Alert variant="destructive">
                        <ExclamationTriangleIcon className="h-4 w-4" />
                        <AlertTitle>Payment Loading Errors</AlertTitle>
                        <AlertDescription>
                          <ul>
                            {paymentsResult.errors.map((error, index) => (
                              <li key={index}>{error}</li>
                            ))}
                          </ul>
                        </AlertDescription>
                      </Alert>
                    )}

                    {paymentsResult && (
                      <div className="border p-4 rounded-lg">
                        <h3 className="text-lg font-semibold mb-4">
                          Payment History ({paymentsResult.payments.length + paymentsResult.charges.length} total)
                        </h3>

                        {/* Payment Intents */}
                        {paymentsResult.payments.length > 0 && (
                          <div className="mb-6">
                            <h4 className="font-medium mb-3">Payment Intents ({paymentsResult.payments.length})</h4>
                            <div className="space-y-3">
                              {paymentsResult.payments.map((payment) => (
                                <div key={payment.id} className="bg-gray-50 p-3 rounded border border-gray-200 text-sm">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <div>
                                      <p><strong>ID:</strong> {payment.id}</p>
                                      <p><strong>Amount:</strong> {formatCurrency(payment.amount, payment.currency)}</p>
                                      <p><strong>Status:</strong> <span className={`font-semibold ${getPaymentStatusColor(payment.status)}`}>{payment.status}</span></p>
                                      <p><strong>Created:</strong> {formatTimestamp(payment.created)}</p>
                                    </div>
                                    <div>
                                      {payment.description && <p><strong>Description:</strong> {payment.description}</p>}
                                      {payment.receipt_email && <p><strong>Receipt Email:</strong> {payment.receipt_email}</p>}
                                      {payment.payment_method && (
                                        <p><strong>Payment Method:</strong> {typeof payment.payment_method === 'string' ? payment.payment_method : payment.payment_method.id}</p>
                                      )}
                                      {payment.invoice && (
                                        <p><strong>Invoice:</strong> {typeof payment.invoice === 'string' ? payment.invoice : payment.invoice.id}</p>
                                      )}
                                    </div>
                                  </div>
                                  {/* Refund Button */}
                                  {payment.status === 'succeeded' && (
                                    <div className="mt-3 pt-2 border-t border-gray-300">
                                      <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={() => handleInitiateRefund(
                                          payment.id,
                                          payment.amount,
                                          payment.currency,
                                          payment.created,
                                          'payment'
                                        )}
                                        disabled={refundLoading && refundingPaymentId === payment.id}
                                      >
                                        {refundLoading && refundingPaymentId === payment.id ? 'Processing...' : 'Refund Payment'}
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Charges (for older payments) */}
                        {paymentsResult.charges.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-3">Charges ({paymentsResult.charges.length})</h4>
                            <div className="space-y-3">
                              {paymentsResult.charges.map((charge) => (
                                <div key={charge.id} className="bg-gray-50 p-3 rounded border border-gray-200 text-sm">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    <div>
                                      <p><strong>ID:</strong> {charge.id}</p>
                                      <p><strong>Amount:</strong> {formatCurrency(charge.amount, charge.currency)}</p>
                                      <p><strong>Status:</strong> <span className={`font-semibold ${getPaymentStatusColor(charge.status || 'unknown')}`}>{charge.status || 'unknown'}</span></p>
                                      <p><strong>Created:</strong> {formatTimestamp(charge.created)}</p>
                                    </div>
                                    <div>
                                      {charge.description && <p><strong>Description:</strong> {charge.description}</p>}
                                      {charge.receipt_email && <p><strong>Receipt Email:</strong> {charge.receipt_email}</p>}
                                      {charge.payment_method && <p><strong>Payment Method:</strong> {charge.payment_method}</p>}
                                      {charge.invoice && (
                                        <p><strong>Invoice:</strong> {typeof charge.invoice === 'string' ? charge.invoice : charge.invoice?.id}</p>
                                      )}
                                      {charge.refunded && <p className="text-orange-600"><strong>Refunded:</strong> Yes</p>}
                                    </div>
                                  </div>
                                  {/* Refund Button */}
                                  {charge.status === 'succeeded' && !charge.refunded && (
                                    <div className="mt-3 pt-2 border-t border-gray-300">
                                      <Button
                                        variant="destructive"
                                        size="sm"
                                        onClick={() => handleInitiateRefund(
                                          charge.id,
                                          charge.amount,
                                          charge.currency,
                                          charge.created,
                                          'charge'
                                        )}
                                        disabled={refundLoading && refundingPaymentId === charge.id}
                                      >
                                        {refundLoading && refundingPaymentId === charge.id ? 'Processing...' : 'Refund Payment'}
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {paymentsResult.payments.length === 0 && paymentsResult.charges.length === 0 && (
                          <p className="text-gray-500 text-center py-4">No payment history found for this customer.</p>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Advanced User Data Panel */}
            {searchResult && (
              <div>
                <Button
                  variant="outline"
                  className="w-full justify-between mt-6"
                  onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
                >
                  Advanced User Data
                  {isAdvancedOpen ? <ChevronDownIcon className="h-4 w-4" /> : <ChevronRightIcon className="h-4 w-4" />}
                </Button>
                {isAdvancedOpen && (
                  <div className="space-y-6 mt-4">
                  {/* Display any errors from the search */}
                  {searchResult.errors && searchResult.errors.length > 0 && (
                    <Alert variant="destructive">
                      <ExclamationTriangleIcon className="h-4 w-4" />
                      <AlertTitle>Search Errors</AlertTitle>
                      <AlertDescription>
                        <ul>
                          {searchResult.errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Firebase Auth Section */}
                  {searchResult.firebaseUsers.length > 0 && (
                    <div className="border p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Firebase Auth ({searchResult.firebaseUsers.length} users)</h3>
                      <div className="space-y-6">
                        {searchResult.firebaseUsers.map((user: FirebaseUser) => (
                          <div key={user.id} className="border-l-4 border-blue-500 pl-4 py-2 space-y-3">
                            <div className="space-y-2">
                              <div>ID: {user.id}</div>
                              <div>Email: {user.email}</div>
                              {user.createdAt && <div>Created: {user.createdAt.toLocaleDateString()}</div>}
                              {user.lastLoginAt && <div>Last Login: {user.lastLoginAt.toLocaleDateString()}</div>}

                              {user.providerData && user.providerData.length > 0 && (
                                <div className="mt-2">
                                  <h4 className="font-medium mb-1">Login Providers</h4>
                                  {user.providerData.map((provider: { providerId: string; uid: string }, index: number) => (
                                    <div key={index} className="ml-4">
                                      <div>Provider: {provider.providerId}</div>
                                      <div>Provider ID: {provider.uid}</div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>

                            {/* Display Custom Claims */}
                            {user.customClaims && (
                              <div className="mt-3">
                                <h4 className="font-medium mb-1">Custom Claims</h4>
                                <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                                  {JSON.stringify(user.customClaims, null, 2)}
                                </pre>
                              </div>
                            )}

                            {/* Set Admin Button */}
                            <div className="mt-3">
                              <LoadingButton
                                size="sm"
                                onClick={() => handleSetAdmin(user.id)}
                                loading={settingAdminUserId === user.id}
                                disabled={settingAdminUserId === user.id || user.customClaims?.cookiecadPermissions?.isAdmin === true}
                                className={user.customClaims?.cookiecadPermissions?.isAdmin === true ? "bg-gray-400 cursor-not-allowed" : "bg-purple-600 hover:bg-purple-700"}
                              >
                                {settingAdminUserId === user.id
                                  ? 'Setting...'
                                  : user.customClaims?.cookiecadPermissions?.isAdmin === true
                                  ? 'Already Admin'
                                  : 'Set as Admin'}
                              </LoadingButton>
                            </div>

                            {/* Display Subscriptions */}
                            {(() => {
                              console.log('Checking subscriptions for user:', user.id);
                              console.log('All subscription data:', searchResult.subscriptionData);
                              const userSubs = searchResult.subscriptionData.filter(sub => sub.id === user.id);
                              console.log('Filtered subscriptions:', userSubs);
                              return userSubs.length > 0 ? (
                                <div className="mt-4">
                                  <h4 className="font-medium mb-2">Subscriptions</h4>
                                  {userSubs.map((sub) => (
                                    <pre key={sub.id} className="bg-gray-100 p-2 rounded text-sm overflow-auto">
                                      {JSON.stringify(sub, null, 2)}
                                    </pre>
                                  ))}
                                </div>
                              ) : null;
                            })()}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Live Stripe Subscriptions */}
                  {searchResult.liveStripeSubscriptions && searchResult.liveStripeSubscriptions.length > 0 && (
                    <div className="border p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Live Stripe Subscriptions</h3>
                      {searchResult.liveStripeSubscriptions.map((sub) => (
                        <div key={sub.id} className="bg-gray-50 p-3 rounded border border-gray-200 mb-3 text-sm">
                          <p><strong>Subscription ID:</strong> {sub.id}</p>
                          <p><strong>Status:</strong> <span className={`font-semibold ${sub.status === 'active' ? 'text-green-600' : sub.status === 'canceled' ? 'text-red-600' : 'text-yellow-600'}`}>{sub.status}</span></p>
                          <p><strong>Plan:</strong> {getStripePlanDisplay(sub)}</p>
                          <p><strong>Current Period End:</strong> {formatTimestamp(sub.current_period_end)}</p>
                          {sub.cancel_at_period_end && sub.cancel_at && (
                            <p className="text-orange-600"><strong>Cancels At Period End:</strong> Yes ({formatTimestamp(sub.cancel_at)})</p>
                          )}
                          {!sub.cancel_at_period_end && sub.cancel_at && (
                            <p className="text-red-600"><strong>Cancelled At:</strong> {formatTimestamp(sub.cancel_at)}</p>
                          )}

                          {/* Cancel Button - Show for ANY active subscription */}
                          {sub.status === 'active' && (
                            <Button
                              variant="destructive"
                              size="sm"
                              className="mt-2"
                              onClick={() => handleInitiateCancellationFromSummary(sub)}
                              disabled={cancelLoading}
                            >
                              {cancelLoading ? 'Processing...' : 'Cancel Plan'}
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Auth0 Section */}
                  {searchResult.auth0Users.length > 0 && (
                    <div className="border p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Auth0</h3>
                      {searchResult.auth0Users.map((user) => (
                        <div key={user.user_id} className="space-y-4">
                          <div>
                            <div>ID: {user.user_id}</div>
                            <div>Email: {user.email}</div>
                            <div>Name: {user.name}</div>
                            <div>Email Verified: {user.email_verified ? 'Yes' : 'No'}</div>
                            <div>Last Login: {new Date(user.last_login).toLocaleDateString()}</div>
                            <div>Login Count: {user.logins_count}</div>
                          </div>

                          {user.app_metadata && (
                            <div>
                              <h4 className="font-medium mb-2">App Metadata</h4>
                              <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
                                {JSON.stringify(user.app_metadata, null, 2)}
                              </pre>
                            </div>
                          )}

                          {user.identities && user.identities.length > 0 && (
                            <div>
                              <h4 className="font-medium mb-2">Linked Accounts</h4>
                              {user.identities.map((identity: any) => (
                                <div key={identity.user_id} className="ml-4">
                                  <div>Provider: {identity.provider}</div>
                                  <div>ID: {identity.user_id}</div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Stripe Customer Information */}
                  {searchResult.stripeCustomers.length > 0 && (
                    <div className="border p-4 rounded-lg">
                      <h3 className="text-lg font-semibold mb-4">Stripe Customers</h3>

                      {/* Linked Stripe Customers */}
                      {searchResult.stripeCustomers.filter(customer => customer.isLinked).length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-medium mb-2">Linked Stripe Customers</h4>
                          <div className="space-y-4">
                            {searchResult.stripeCustomers
                              .filter(customer => customer.isLinked)
                              .map(customer => (
                                <div key={customer.id} className="border-l-4 border-green-500 pl-4 py-2">
                                  <div className="space-y-1">
                                    <div>ID: {customer.id}</div>
                                    <div>Email: {customer.email}</div>
                                    {customer.name && <div>Name: {customer.name}</div>}
                                    {customer.phone && <div>Phone: {customer.phone}</div>}
                                    <div>Created: {new Date(customer.created * 1000).toLocaleDateString()}</div>

                                    {customer.metadata && Object.keys(customer.metadata).length > 0 && (
                                      <div className="mt-2">
                                        <h5 className="font-medium mb-1">Metadata</h5>
                                        <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                                          {JSON.stringify(customer.metadata, null, 2)}
                                        </pre>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}

                      {/* Unlinked Stripe Customers */}
                      {searchResult.stripeCustomers.filter(customer => !customer.isLinked).length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2 text-orange-600 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                            </svg>
                            Unlinked Stripe Accounts Found!
                          </h4>
                          <div className="bg-orange-50 border border-orange-200 rounded-md p-3 mb-3">
                            <p className="text-sm text-orange-700">
                              These Stripe accounts have the same email but are not linked to any Auth0 or Firestore records.
                              They may need to be linked or represent orphaned accounts.
                            </p>
                          </div>
                          <div className="space-y-4">
                            {searchResult.stripeCustomers
                              .filter(customer => !customer.isLinked)
                              .map(customer => (
                                <div key={customer.id} className="border-l-4 border-orange-500 pl-4 py-2">
                                  <div className="space-y-1">
                                    <div>ID: {customer.id}</div>
                                    <div>Email: {customer.email}</div>
                                    {customer.name && <div>Name: {customer.name}</div>}
                                    {customer.phone && <div>Phone: {customer.phone}</div>}
                                    <div>Created: {new Date(customer.created * 1000).toLocaleDateString()}</div>

                                    {customer.metadata && Object.keys(customer.metadata).length > 0 && (
                                      <div className="mt-2">
                                        <h5 className="font-medium mb-1">Metadata</h5>
                                        <pre className="bg-gray-100 p-2 rounded text-xs overflow-auto">
                                          {JSON.stringify(customer.metadata, null, 2)}
                                        </pre>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  </div>
                )}
              </div>
            )}

            {/* Email Change Section */}
            {searchResult && (
              <div className="border p-4 rounded-lg mt-6">
                <h3 className="text-lg font-semibold mb-4">Change Email Address</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label htmlFor="currentEmail" className="block text-sm font-medium text-gray-700 mb-1">
                        Current Email
                      </label>
                      <input
                        id="currentEmail"
                        type="email"
                        value={currentEmail}
                        onChange={(e) => setCurrentEmail(e.target.value)}
                        className="w-full px-4 py-2 border rounded-lg"
                        disabled={changingEmail}
                      />
                    </div>
                    <div>
                      <label htmlFor="newEmail" className="block text-sm font-medium text-gray-700 mb-1">
                        New Email
                      </label>
                      <input
                        id="newEmail"
                        type="email"
                        value={newEmail}
                        onChange={(e) => setNewEmail(e.target.value)}
                        className="w-full px-4 py-2 border rounded-lg"
                        disabled={changingEmail}
                      />
                    </div>
                  </div>
                  <div className="mt-2">
                    <LoadingButton
                      onClick={handleChangeEmail}
                      loading={changingEmail}
                      className="bg-orange-500 hover:bg-orange-600"
                    >
                      Update Email Address
                    </LoadingButton>
                  </div>
                  <div className="text-sm text-gray-600 mt-2">
                    <p>This will update the email address across multiple systems:</p>
                    <ul className="list-disc list-inside ml-2">
                      <li>Auth0 user accounts</li>
                      <li>Firebase Auth</li>
                      <li>Subscription records in Firestore</li>
                      <li>Stripe customer accounts (if found)</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {/* Refund Confirmation Dialog */}
            <AlertDialog open={isRefundConfirmOpen} onOpenChange={(open) => {
              if (!open) {
                setIsRefundConfirmOpen(false);
                setRefundingPaymentId(null);
                setRefundPaymentData(null);
              }
            }}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Refund Payment?</AlertDialogTitle>
                  <AlertDialogDescription>
                    {refundPaymentData && (
                      <>
                        This payment of <strong>{formatCurrency(refundPaymentData.amount, refundPaymentData.currency)}</strong> was made{' '}
                        <strong>{getDaysAgo(refundPaymentData.created)} days ago</strong>. Continue to refund it?
                        <br /><br />
                        <span className="text-sm text-muted-foreground">
                          Payment ID: {refundPaymentData.id}
                        </span>
                      </>
                    )}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={() => {
                    setIsRefundConfirmOpen(false);
                    setRefundingPaymentId(null);
                    setRefundPaymentData(null);
                  }}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleConfirmRefund}
                    disabled={refundLoading}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {refundLoading ? 'Processing...' : `Refund ${refundPaymentData ? formatCurrency(refundPaymentData.amount, refundPaymentData.currency) : ''}`}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            {/* Subscription Cancellation Dialog */}
            <AlertDialog open={isCancelConfirmOpen} onOpenChange={(open) => {
              if (!open) {
                setIsCancelConfirmOpen(false);
                setCancellingSubscriptionId(null);
                setCancelWithRefund(false);
                setCancelAndRefundLastPayment(false);
                setLastPaymentData(null);
              }
            }}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Cancel Subscription?</AlertDialogTitle>
                  <AlertDialogDescription>
                    {cancellingSubscriptionId && (
                      <>
                        Subscription ID: {cancellingSubscriptionId}
                        {searchResult?.liveStripeSubscriptions.find(sub => sub.id === cancellingSubscriptionId)?.cancel_at_period_end && (
                          <><br /><span className="text-orange-600">This subscription is already set to cancel at period end.</span></>
                        )}
                        <br /><br />Choose how to proceed:
                      </>
                    )}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="py-4 space-y-4">
                  <RadioGroup
                    value={cancelWithRefund ? 'immediate' : 'period_end'}
                    onValueChange={(value) => setCancelWithRefund(value === 'immediate')}
                    className="space-y-3"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="period_end" id="cancel-period-end" />
                      <Label htmlFor="cancel-period-end">
                        Cancel at end of current period
                        {searchResult?.liveStripeSubscriptions.find(sub => sub.id === cancellingSubscriptionId)?.current_period_end && (
                          <span className="text-sm text-muted-foreground ml-1">
                            ({formatTimestamp(searchResult.liveStripeSubscriptions.find(sub => sub.id === cancellingSubscriptionId)?.current_period_end)})
                          </span>
                        )}
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="immediate" id="cancel-immediate" />
                      <Label htmlFor="cancel-immediate">Cancel immediately</Label>
                    </div>
                  </RadioGroup>

                  {/* Refund Last Payment Option */}
                  {lastPaymentData && (
                    <div className="border-t pt-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="refund-last-payment"
                          checked={cancelAndRefundLastPayment}
                          onCheckedChange={(checked) => setCancelAndRefundLastPayment(checked as boolean)}
                        />
                        <Label htmlFor="refund-last-payment" className="text-sm">
                          Also refund the last payment of{' '}
                          <strong>{formatCurrency(lastPaymentData.amount, lastPaymentData.currency)}</strong>{' '}
                          made <strong>{getDaysAgo(lastPaymentData.created)} days ago</strong>
                        </Label>
                      </div>
                      <p className="text-xs text-muted-foreground mt-2 ml-6">
                        Payment ID: {lastPaymentData.id}
                      </p>
                    </div>
                  )}

                  {/* Debug info */}
                  {!lastPaymentData && (
                    <div className="text-xs text-muted-foreground">
                      Debug: No last payment data found. Payments loaded: {paymentsResult ? 'Yes' : 'No'}
                      {paymentsResult && ` (${paymentsResult.payments.length} payments, ${paymentsResult.charges.length} charges)`}
                    </div>
                  )}

                  <p className="text-xs text-muted-foreground">
                    Note: Immediate cancellation deletes the subscription in Stripe.
                    {cancelAndRefundLastPayment && ' The refund will be processed separately after cancellation.'}
                  </p>
                </div>
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={() => {
                    setIsCancelConfirmOpen(false);
                    setCancellingSubscriptionId(null);
                    setCancelWithRefund(false);
                    setCancelAndRefundLastPayment(false);
                    setLastPaymentData(null);
                  }}>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleCancelSubscription}
                    disabled={cancelLoading}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {cancelLoading ? 'Processing...' : 'Confirm Cancellation'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </div>
    </main>
  );
}

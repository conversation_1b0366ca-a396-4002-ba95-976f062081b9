"use server"

import admin from "firebase-admin";
import env from "~/env";
import { getUserByEmail } from "../../api/users/auth0";
import { ManagementClient } from 'auth0';
import Stripe from 'stripe';

// Initialize Firebase Admin for auth project
const authApp = admin.apps.find(app => app?.name === 'auth-app') || admin.initializeApp({
  credential: admin.credential.cert({
    projectId: env.AUTH_FIREBASE_PROJECT_ID,
    clientEmail: env.AUTH_FIREBASE_ADMIN_CLIENT_EMAIL,
    privateKey: env.AUTH_FIREBASE_ADMIN_PRIVATE_KEY.replace(/\\n/g, '\n'),
  }),
}, 'auth-app');

// Initialize Auth0 Management client
const auth0Management = new ManagementClient({
  domain: env.AUTH0_DOMAIN as string,
  clientId: env.AUTH0_CLIENT_ID as string,
  clientSecret: env.AUTH0_CLIENT_SECRET as string,
});

// Initialize Stripe client
const stripe = new Stripe(env.STRIPE_SECRET_KEY, {
  apiVersion: '2022-08-01',
});

export interface Subscription {
  id: string;
  userId: string;
  email: string;
  stripeCustomerId: string;
  subscriptionPeriodEndDate: string;
  subscriptionStatus: string;
  auth0Id: string;
  createdAt: string;
  updatedAt: string;
  subscriptionPlan: string;
}

export interface StripeCustomer {
  id: string;
  email: string;
  name?: string;
  phone?: string;
  metadata?: Record<string, string>;
  created: number;
  isLinked: boolean;
}

// Define FirebaseUser type separately for export
export interface FirebaseUser {
  id: string;
  email: string;
  createdAt?: Date;
  lastLoginAt?: Date;
  providerData?: {
    providerId: string;
    uid: string;
  }[];
  customClaims?: { [key: string]: any } | null;
}

export interface UserSearchResult {
  firebaseUsers: FirebaseUser[];
  subscriptionData: Subscription[];
  auth0Users: any[];
  stripeCustomers: StripeCustomer[];
  linkedStripeCustomerIds: Set<string>;
  liveStripeSubscriptions: Stripe.Subscription[];
  errors: string[];
}

export interface ChangeEmailResult {
  success: boolean;
  message: string;
  updates: {
    firebase?: {
      success: boolean;
      message: string;
    },
    auth0?: {
      success: boolean;
      message: string;
      users: string[];
    },
    subscriptions?: {
      success: boolean;
      message: string;
      updated: string[];
    },
    stripe?: {
      success: boolean;
      message: string;
      customerId?: string;
    }
  };
  errors: string[];
}

export interface CancelSubscriptionResult {
  success: boolean;
  message: string;
  error?: string;
}

export interface SetAdminResult {
  success: boolean;
  message: string;
  error?: string;
}

export interface CustomerPaymentsResult {
  payments: Stripe.PaymentIntent[];
  charges: Stripe.Charge[];
  errors: string[];
}

export async function searchUserByEmail(email: string): Promise<UserSearchResult> {
  try {
    const result: UserSearchResult = {
      firebaseUsers: [],
      subscriptionData: [],
      auth0Users: [],
      stripeCustomers: [],
      linkedStripeCustomerIds: new Set<string>(),
      liveStripeSubscriptions: [],
      errors: []
    };

    // Search Firebase Auth
    try {
      const allUsers: admin.auth.UserRecord[] = [];
      let pageToken: string | undefined;

      // Keep fetching users until we've got them all
      do {
        const listUsersResult = await authApp.auth().listUsers(1000, pageToken);
        allUsers.push(...listUsersResult.users);
        pageToken = listUsersResult.pageToken;
      } while (pageToken);

      // Filter users with matching email
      const matchingUsers = allUsers.filter(user => user.email?.toLowerCase() === email.toLowerCase());
      
      result.firebaseUsers = matchingUsers.map(user => ({
        id: user.uid,
        email: user.email || '',
        createdAt: user.metadata.creationTime ? new Date(user.metadata.creationTime) : undefined,
        lastLoginAt: user.metadata.lastSignInTime ? new Date(user.metadata.lastSignInTime) : undefined,
        providerData: user.providerData.map(provider => ({
          providerId: provider.providerId,
          uid: provider.uid
        })),
        customClaims: user.customClaims || null
      }));

      // Search Firestore for subscription data for all matching users
      console.log('Searching subscriptions for users:', result.firebaseUsers.map(u => u.id));
      
      // Get subscriptions by document ID (which is the Firebase user ID)
      const subscriptionsPromises = result.firebaseUsers.map(async user => {
        const doc = await authApp.firestore().doc(`subscriptions/${user.id}`).get();
        return doc.exists ? [{ id: doc.id, ...doc.data() }] : [];
      });

      const allSubscriptions = await Promise.all(subscriptionsPromises);
      result.subscriptionData = processSubscriptions(allSubscriptions.flat() as Subscription[]);
      console.log('Subscription data:', result.subscriptionData);
    } catch (error) {
      console.error('Error searching Firebase:', error);
    }

    // Search Auth0
    try {
      const auth0Response = await getUserByEmail(email);
      if (auth0Response?.data) {
        result.auth0Users = auth0Response.data;

        // Collect Stripe customer IDs from Auth0 metadata
        for (const auth0User of result.auth0Users) {
          if (auth0User.app_metadata?.stripeCustomerId) {
            result.linkedStripeCustomerIds.add(auth0User.app_metadata.stripeCustomerId);
          }
        }
      }
    } catch (error) {
      console.error('Error searching Auth0:', error);
    }
    
    // Collect Stripe customer IDs from subscription data
    for (const subscription of result.subscriptionData) {
      if (subscription.stripeCustomerId) {
        result.linkedStripeCustomerIds.add(subscription.stripeCustomerId);
      }
    }
    
    // Search Stripe for customers with the email
    try {
      console.log(`Searching Stripe for customers with email: ${email}`);
      const stripeCustomers = await stripe.customers.list({
        email: email.toLowerCase(),
        limit: 100
      });
      
      // Process Stripe customers
      for (const customer of stripeCustomers.data) {
        const isLinked = result.linkedStripeCustomerIds.has(customer.id);
        result.stripeCustomers.push({
          id: customer.id,
          email: customer.email || '',
          name: customer.name || undefined,
          phone: customer.phone || undefined,
          metadata: customer.metadata || undefined,
          created: customer.created,
          isLinked
        });
        
        // If this customer is linked, make sure it's in our linkedStripeCustomerIds set
        if (isLinked) {
          result.linkedStripeCustomerIds.add(customer.id);
        }
      }
      
      console.log(`Found ${result.stripeCustomers.length} Stripe customers, ${result.linkedStripeCustomerIds.size} linked`);
    } catch (error) {
      console.error('Error searching Stripe:', error);
    }

    // Fetch live Stripe subscriptions for linked customers
    try {
      console.log(`Fetching live subscriptions for linked customer IDs: ${[...result.linkedStripeCustomerIds]}`);
      const subscriptionPromises = [...result.linkedStripeCustomerIds].map(customerId =>
        stripe.subscriptions.list({ customer: customerId, status: 'all', limit: 100 })
      );
      const subscriptionResults = await Promise.all(subscriptionPromises);
      result.liveStripeSubscriptions = subscriptionResults.flatMap(res => res.data);
      console.log(`Found ${result.liveStripeSubscriptions.length} live Stripe subscriptions.`);
    } catch (error) {
       console.error('Error fetching live Stripe subscriptions:', error);
       const errorMessage = error instanceof Error ? error.message : String(error);
       result.errors.push(`Error fetching live Stripe subscriptions: ${errorMessage}`);
    }

    return result;
  } catch (error) {
    console.error('Error in searchUserByEmail:', error);
    throw error;
  }
}

function processSubscriptions(subscriptions: any[]): Subscription[] {
  return subscriptions.flat().map(sub => ({
    ...sub,
    subscriptionPeriodEndDate: new Date(sub.subscriptionPeriodEndDate)?.toLocaleDateString(),
    createdAt: new Date(sub.createdAt)?.toLocaleDateString(),
    updatedAt: new Date(sub.updatedAt)?.toLocaleDateString(),
  }));
}

/**
 * Changes a user's email address across all systems (Auth0, Firebase, Firestore)
 * @param currentEmail The current email address
 * @param newEmail The new email address to change to
 * @returns Result object with status and details of each update operation
 */
export async function changeUserEmail(currentEmail: string, newEmail: string): Promise<ChangeEmailResult> {
  console.log(`Attempting to change email from ${currentEmail} to ${newEmail}`);

  const result: ChangeEmailResult = {
    success: false,
    message: "",
    updates: {},
    errors: []
  };

  try {
    // 1. First search for all user records with the current email
    const searchResult = await searchUserByEmail(currentEmail);
    console.log("Search results:", JSON.stringify(searchResult, null, 2));

    if (
      searchResult.auth0Users.length === 0 &&
      searchResult.firebaseUsers.length === 0
    ) {
      result.message = `No users found with email: ${currentEmail}`;
      result.errors.push("No users found to update");
      return result;
    }

    // 2. Update Auth0 users
    result.updates.auth0 = { success: false, message: "", users: [] };
    try {
      const updatedAuth0Users: string[] = [];

      for (const auth0User of searchResult.auth0Users) {
        try {
          await auth0Management.users.update(
            { id: auth0User.user_id },
            { email: newEmail }
          );
          updatedAuth0Users.push(auth0User.user_id);
        } catch (error: any) {
          console.error(`Error updating Auth0 user ${auth0User.user_id}:`, error);
          result.errors.push(`Auth0 update failed for user ${auth0User.user_id}: ${error.message}`);
        }
      }

      if (updatedAuth0Users.length > 0) {
        result.updates.auth0.success = true;
        result.updates.auth0.message = `Updated ${updatedAuth0Users.length} Auth0 users`;
        result.updates.auth0.users = updatedAuth0Users;
      } else if (searchResult.auth0Users.length > 0) {
        result.updates.auth0.message = "Failed to update any Auth0 users";
      } else {
        result.updates.auth0.message = "No Auth0 users to update";
      }
    } catch (error: any) {
      console.error("Error updating Auth0 users:", error);
      result.updates.auth0.message = `Error updating Auth0 users: ${error.message}`;
      result.errors.push(`Auth0 update error: ${error.message}`);
    }

    // 3. Update Firebase Auth users
    result.updates.firebase = { success: false, message: "" };
    try {
      let firebaseUpdateCount = 0;

      for (const firebaseUser of searchResult.firebaseUsers) {
        try {
          await authApp.auth().updateUser(firebaseUser.id, {
            email: newEmail,
          });
          firebaseUpdateCount++;
        } catch (error: any) {
          console.error(`Error updating Firebase user ${firebaseUser.id}:`, error);
          result.errors.push(`Firebase update failed for user ${firebaseUser.id}: ${error.message}`);
        }
      }

      if (firebaseUpdateCount > 0) {
        result.updates.firebase.success = true;
        result.updates.firebase.message = `Updated ${firebaseUpdateCount} Firebase users`;
      } else if (searchResult.firebaseUsers.length > 0) {
        result.updates.firebase.message = "Failed to update any Firebase users";
      } else {
        result.updates.firebase.message = "No Firebase users to update";
      }
    } catch (error: any) {
      console.error("Error updating Firebase users:", error);
      result.updates.firebase.message = `Error updating Firebase users: ${error.message}`;
      result.errors.push(`Firebase update error: ${error.message}`);
    }

    // 4. Update Subscription data in Firestore
    result.updates.subscriptions = { success: false, message: "", updated: [] };
    try {
      const updatedSubscriptions: string[] = [];

      for (const subscription of searchResult.subscriptionData) {
        try {
          await authApp.firestore().doc(`subscriptions/${subscription.id}`).update({
            email: newEmail
          });
          updatedSubscriptions.push(subscription.id);
        } catch (error: any) {
          console.error(`Error updating subscription for ${subscription.id}:`, error);
          result.errors.push(`Subscription update failed for ${subscription.id}: ${error.message}`);
        }
      }

      if (updatedSubscriptions.length > 0) {
        result.updates.subscriptions.success = true;
        result.updates.subscriptions.message = `Updated ${updatedSubscriptions.length} subscriptions`;
        result.updates.subscriptions.updated = updatedSubscriptions;
      } else if (searchResult.subscriptionData.length > 0) {
        result.updates.subscriptions.message = "Failed to update any subscriptions";
      } else {
        result.updates.subscriptions.message = "No subscriptions to update";
      }
    } catch (error: any) {
      console.error("Error updating subscriptions:", error);
      result.updates.subscriptions.message = `Error updating subscriptions: ${error.message}`;
      result.errors.push(`Subscription update error: ${error.message}`);
    }

    // 5. Update Stripe customer email if available
    result.updates.stripe = { success: false, message: "" };
    try {
      // Collect Stripe customer IDs from various sources
      const stripeCustomerIds = new Set<string>();

      // From Auth0 user metadata
      for (const auth0User of searchResult.auth0Users) {
        if (auth0User.app_metadata?.stripeCustomerId) {
          stripeCustomerIds.add(auth0User.app_metadata.stripeCustomerId);
        }
      }

      // From subscription data
      for (const subscription of searchResult.subscriptionData) {
        if (subscription.stripeCustomerId) {
          stripeCustomerIds.add(subscription.stripeCustomerId);
        }
      }

      console.log("Found Stripe customer IDs:", [...stripeCustomerIds]);

      if (stripeCustomerIds.size > 0) {
        let updatedCount = 0;
        let lastCustomerId = "";

        for (const customerId of stripeCustomerIds) {
          try {
            console.log(`Updating Stripe customer ${customerId}`);
            await stripe.customers.update(customerId, {
              email: newEmail
            });
            updatedCount++;
            lastCustomerId = customerId;
          } catch (error: any) {
            console.error(`Error updating Stripe customer ${customerId}:`, error);
            result.errors.push(`Stripe update failed for customer ${customerId}: ${error.message}`);
          }
        }

        if (updatedCount > 0) {
          result.updates.stripe.success = true;
          result.updates.stripe.message = `Updated ${updatedCount} Stripe customers`;
          result.updates.stripe.customerId = lastCustomerId;
        } else {
          result.updates.stripe.message = "Failed to update any Stripe customers";
        }
      } else {
        result.updates.stripe.message = "No Stripe customers found to update";
      }
    } catch (error: any) {
      console.error("Error updating Stripe:", error);
      result.updates.stripe.message = `Error updating Stripe: ${error.message}`;
      result.errors.push(`Stripe update error: ${error.message}`);
    }

    // Determine overall success
    result.success = result.errors.length === 0;
    result.message = result.success
      ? `Successfully changed email from ${currentEmail} to ${newEmail}`
      : `Completed with ${result.errors.length} errors`;

    return result;
  } catch (error: any) {
    console.error("Error in changeUserEmail:", error);
    result.message = `Error changing email: ${error.message}`;
    result.errors.push(error.message);
    return result;
  }
}

/**
 * Cancels a Stripe subscription.
 * @param subscriptionId The ID of the Stripe subscription to cancel.
 * @param cancelImmediately If true, cancels immediately (potentially with refund). If false, cancels at period end.
 * @returns Result object indicating success or failure.
 */
export async function cancelStripeSubscription(
  subscriptionId: string,
  cancelImmediately: boolean
): Promise<CancelSubscriptionResult> {
  console.log(`Attempting to cancel subscription ${subscriptionId}. Cancel immediately: ${cancelImmediately}`);

  try {
    if (cancelImmediately) {
      // Cancel immediately - Note: Refunding is complex and might need separate handling/confirmation
      // For now, just deleting the subscription. Add refund logic if specifically required.
      await stripe.subscriptions.del(subscriptionId);
      console.log(`Subscription ${subscriptionId} cancelled immediately.`);
      return { success: true, message: "Subscription cancelled immediately." };
    } else {
      // Cancel at period end
      await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
      });
      console.log(`Subscription ${subscriptionId} set to cancel at period end.`);
      return { success: true, message: "Subscription set to cancel at period end." };
    }
  } catch (error: any) {
    console.error(`Error cancelling subscription ${subscriptionId}:`, error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return { success: false, message: `Failed to cancel subscription: ${errorMessage}`, error: errorMessage };
  }
}

/**
 * Sets a Firebase user as an admin by adding a custom claim.
 *
 * @param userId The Firebase UID of the user to set as admin.
 * @returns {Promise<SetAdminResult>} Result object indicating success or failure.
 */
export async function setUserAdmin(userId: string): Promise<SetAdminResult> {
  console.log(`Attempting to set user ${userId} as admin.`);
  try {
    // Get existing claims to merge, preserving other claims
    const user = await authApp.auth().getUser(userId);
    const existingClaims = user.customClaims || {};

    // Define the admin claim structure
    const adminClaim = {
      cookiecadPermissions: {
        ...(existingClaims.cookiecadPermissions || {}), // Preserve existing permissions
        isAdmin: true,
      },
    };

    // Merge with other existing claims at the root level
    const newClaims = {
      ...existingClaims,
      ...adminClaim,
    };

    // Set the custom claims
    await authApp.auth().setCustomUserClaims(userId, newClaims);

    console.log(`Successfully set custom claims for user ${userId}:`, newClaims);
    return { success: true, message: `User ${userId} successfully set as admin.` };
  } catch (error: any) {
    console.error(`Error setting admin claim for user ${userId}:`, error);
    const errorMessage = error.message || 'An unknown error occurred.';
    return { success: false, message: `Failed to set user ${userId} as admin: ${errorMessage}`, error: errorMessage };
  }
}

/**
 * Fetches all payments for the given Stripe customer IDs
 * @param customerIds Array of Stripe customer IDs
 * @returns Result object with payments and charges
 */
export async function getCustomerPayments(customerIds: string[]): Promise<CustomerPaymentsResult> {
  console.log(`Fetching payments for customer IDs: ${customerIds}`);

  const result: CustomerPaymentsResult = {
    payments: [],
    charges: [],
    errors: []
  };

  if (customerIds.length === 0) {
    return result;
  }

  try {
    // Fetch payment intents for each customer
    const paymentPromises = customerIds.map(async (customerId) => {
      try {
        const payments = await stripe.paymentIntents.list({
          customer: customerId,
          limit: 100 // Adjust limit as needed
        });
        return payments.data;
      } catch (error: any) {
        console.error(`Error fetching payments for customer ${customerId}:`, error);
        result.errors.push(`Error fetching payments for customer ${customerId}: ${error.message}`);
        return [];
      }
    });

    // Fetch charges for each customer (for older payments that might not have payment intents)
    const chargePromises = customerIds.map(async (customerId) => {
      try {
        const charges = await stripe.charges.list({
          customer: customerId,
          limit: 100 // Adjust limit as needed
        });
        return charges.data;
      } catch (error: any) {
        console.error(`Error fetching charges for customer ${customerId}:`, error);
        result.errors.push(`Error fetching charges for customer ${customerId}: ${error.message}`);
        return [];
      }
    });

    // Wait for all requests to complete
    const [paymentResults, chargeResults] = await Promise.all([
      Promise.all(paymentPromises),
      Promise.all(chargePromises)
    ]);

    // Flatten and sort payments by created date (newest first)
    result.payments = paymentResults
      .flat()
      .sort((a, b) => b.created - a.created);

    // Flatten and sort charges by created date (newest first)
    result.charges = chargeResults
      .flat()
      .sort((a, b) => b.created - a.created);

    console.log(`Found ${result.payments.length} payment intents and ${result.charges.length} charges`);

    return result;
  } catch (error: any) {
    console.error('Error in getCustomerPayments:', error);
    result.errors.push(`General error: ${error.message}`);
    return result;
  }
}

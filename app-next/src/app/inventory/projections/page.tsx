"use client"

import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useSearchParams, useRouter, usePathname} from 'next/navigation'
import CcGrid from '~/components/cc-grid'
import { 
  ValueGetterParams,
  ColDef,
  CellValueChangedEvent,
  ValueSetterParams
} from "ag-grid-community";
import firestoreClient from '~/data/google/firebase-client'
import {Accordion, AccordionItem, Input, Button} from "@nextui-org/react";
import { toast } from 'sonner';
// We will not import createPurchaseOrder from inventoryUtils anymore.
// The call will be to a new API endpoint.
import { saveInProgressPurchase, getInProgressPurchase, convertInProgressToFormalPurchase } from './projectionsUtils';
import { EnrichedInventoryAnalysisResultItem } from '~/data/google/types';

// Define the type locally since it's used in this component
interface PurchaseItemDetail {
  sku: string;
  productId: string;
  quantityInCases: number;
  quantityInUnits: number;
  unitCost?: number;
  caseCost?: number;
  unitsPerCase?: number;
}

interface Product { // Product as fetched from /api/products
  sku: string;
  skuMaps?: string[];
  // pendingOrderCases is removed, it will be handled by the 'in-progress' purchase document
  [key: string]: any;
}

interface OutstandingItem {
  sku: string;
  total: number;
}

interface OutstandingInvoices {
  items: OutstandingItem[];
}

interface GridDataItem {
  sku: string;
  top3Shop: number; // New
  top3Amz: number; // New
  last_30_days: number;
  projectedQuantity: number;
  monthsRemainingOtw: number;
  monthsRemaining: number; // New: months remaining without OTW
  quantityNeeded: number;
  casesNeeded: number;
  outstanding: number;
  otw: number;
  warehouse: number;
  inventoryAmazon: number; // Renamed from amazon
  rank?: number;
  newOrder?: number;
  unitsPerCase: number; // Will be 12
}

const DEFAULT_MONTHS_PROJECTION = 6;

export default function Page() {
  const [monthsProjection, setMonthsProjection] = useState<number>(DEFAULT_MONTHS_PROJECTION);
  const [columnDefs, setColumnDefs] = useState<ColDef[]>([
    {
      headerName: 'Rank',
      field: 'rank',
      width: 70,
      sortable: false,
      filter: false
    },
    { field: 'sku'},
    { field: 'top3Shop',
      headerName: 'Top 3 Shop Avg Qty',
      width: 150,
      cellDataType: 'number',
      valueFormatter: params => params.value?.toFixed()
    },
    { field: 'top3Amz',
      headerName: 'Top 3 Amz Avg Qty',
      width: 150,
      cellDataType: 'number',
      valueFormatter: params => params.value?.toFixed()
    },
    { field: 'last_30_days',
      cellDataType: 'number',
     },
    { field: 'projectedQuantity',
      headerName: 'Projected Quantity',
      cellDataType: 'number',
    },
    { field: 'monthsRemainingOtw',
      headerName: 'Months Remaining OTW',
      cellDataType: 'number',
      valueFormatter: params => params.value?.toFixed(1)
    },
    { field: 'monthsRemaining',
      headerName: 'Months Remaining',
      cellDataType: 'number',
      valueFormatter: params => params.value?.toFixed(1)
    },
    {
      field: "quantityNeeded",
      cellDataType: 'number',
      editable: true,
    },
    {
      field: "casesNeeded",
      cellDataType: 'number',
    },
    {
      headerName: "New Order (Cases)",
      field: "newOrder",
      editable: true,
      cellDataType: "number",
      width: 150,
      valueSetter: (params: ValueSetterParams<GridDataItem, number | string | undefined | null>) => {
        const editorValue = params.newValue;
        if (editorValue === null || editorValue === undefined || editorValue === '') {
          params.data.newOrder = undefined;
        } else {
          const val = Number(editorValue);
          params.data.newOrder = isNaN(val) ? undefined : Math.floor(val);
        }
        // After local data update, sync the entire "in-progress" purchase
        syncInProgressPurchaseToFirebase();
        return true;
      },
    }
  ]);

  const [rawData, setRawData] = useState<GridDataItem[]>();
  const [gridData, setGridData] = useState<GridDataItem[]>();
  const [missingColors, setMissingColors] = useState<string[]>([]);
  const [recentDates, setRecentDates] = useState<string>();
  const [products, setProducts] = useState<Product[]>([]);
  const gridRef = useRef<any>(null);

  const defaultColDef = useMemo<ColDef>(() => ({
    sortable: true,
    filter: true,
  }), []);

  const sixMonthsAgo = useMemo(() => {
    const date = new Date();
    date.setMonth(date.getMonth() - 6);
    return date;
  }, []);

  useEffect(() => {
    async function getData() {
      try {
        toast.info('Loading data...');
        // Fetch data with individual error handling
        let inventoryDataResult, outstanding, productsResponse, inProgressPurchase;
        
        // Fetch in-progress purchase first
        try {
          inProgressPurchase = await getInProgressPurchase();
          console.log('In-progress purchase:', inProgressPurchase);
        } catch (error) {
          toast.error('Failed to fetch in-progress purchase: ' + (error instanceof Error ? error.message : 'Unknown error'));
          // Continue without it, or decide if it's a critical failure
        }

        try {
          const response = await fetch(`/api/inventory/analysis?fromDate=${sixMonthsAgo.toISOString()}`);
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          // Handle streaming response
          const reader = response.body?.getReader();
          if (!reader) {
            throw new Error('No response body reader available');
          }

          let buffer = '';
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            // Convert the chunk to text and add to buffer
            buffer += new TextDecoder().decode(value);

            // Split buffer into lines
            const lines = buffer.split('\n');
            
            // Process all complete lines except the last one
            for (let i = 0; i < lines.length - 1; i++) {
              const line = lines[i].trim();
              if (!line) continue;

              try {
                const data = JSON.parse(line);
                if (data.success) {
                  inventoryDataResult = data.data;
                } else if (data.error) {
                  throw new Error(data.error);
                }
              } catch (e) {
                // If it's not JSON, it's probably a status message
                toast.info(line);
              }
            }

            // Keep the last (potentially incomplete) line in the buffer
            buffer = lines[lines.length - 1];
          }

          // Process any remaining data in the buffer
          if (buffer.trim()) {
            try {
              const data = JSON.parse(buffer);
              if (data.success) {
                inventoryDataResult = data.data;
              } else if (data.error) {
                throw new Error(data.error);
              }
            } catch (e) {
              // If it's not JSON, it's probably a status message
              toast.info(buffer);
            }
          }

          if (!inventoryDataResult) {
            throw new Error('No inventory data received');
          }
        } catch (error) {
          toast.error('Failed to fetch inventory analysis: ' + (error instanceof Error ? error.message : 'Unknown error'), {
            duration: 60000,
            dismissible: true
          });
          throw error;
        }
        console.log('Inventory data:', inventoryDataResult);
        
        try {
          outstanding = await firestoreClient.get<OutstandingInvoices>('data', 'outstanding-invoices');
        } catch (error) {
          toast.error('Failed to fetch outstanding invoices: ' + (error instanceof Error ? error.message : 'Unknown error'), {
            duration: 60000,
            dismissible: true
          });
          throw error;
        }
        console.log('Outstanding invoices:', outstanding);

        try {
          const response = await fetch("../api/products");
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          productsResponse = await response.json();
        } catch (error) {
          toast.error('Failed to fetch products: ' + (error instanceof Error ? error.message : 'Unknown error'), {
            duration: 60000,
            dismissible: true
          });
          throw error;
        }
        console.log('Products:', productsResponse);

        const { inventoryData, dates } = inventoryDataResult;
        setRecentDates(`Amazon: ${new Date(dates.amzMaxDate).toLocaleDateString()}, Shopify: ${new Date(dates.shopMaxDate).toLocaleDateString()}
        Range: ${new Date(dates.startDate).toLocaleDateString()} - ${new Date(dates.endDate).toLocaleDateString()}
        `);
        console.log('Inventory-analysis dates:', dates);

        let missingColorsList: string[] = [];
        let rawData: GridDataItem[] = [];

        for (const product of productsResponse.products) {
          //Find inventory items
          let inventoryItems = inventoryData.filter((item: EnrichedInventoryAnalysisResultItem) =>
            (item.color === product.sku || product.skuMaps?.includes(item.color ?? ""))
          );

          if (inventoryItems.length === 0) {
            missingColorsList.push(`No Inventory item for Product: ${product.sku}`);
            continue;
          }
          if (inventoryItems.length > 1) {
            missingColorsList.push(`Multiple inventory items for ${product.sku}: ${inventoryItems.map((item: EnrichedInventoryAnalysisResultItem) => item.color).join(', ')}`);
          }

          let inventoryItem = inventoryItems.reduce((acc: EnrichedInventoryAnalysisResultItem, item: EnrichedInventoryAnalysisResultItem) => ({
            ...acc,
            top3Shop: (acc.top3Shop ?? 0) + (item.top3Shop ?? 0),
            top3Amz: (acc.top3Amz ?? 0) + (item.top3Amz ?? 0),
            otw: (acc.otw ?? 0) + (item.otw ?? 0),
            warehouse: (acc.warehouse ?? 0) + (item.warehouse ?? 0),
            inventoryAmazon: (acc.inventoryAmazon ?? 0) + (item.inventoryAmazon ?? 0),
            last_30_days: (acc.last_30_days ?? 0) + (item.last_30_days ?? 0)
          }), {
            color: '',
            topShop: 0,
            topAmz: 0,
            top3Shop: 0,
            top3Amz: 0,
            otw: 0,
            warehouse: 0,
            inventoryAmazon: 0,
            last_30_days: 0
          } as EnrichedInventoryAnalysisResultItem);

          let outstandingItem = outstanding?.items.find((item: OutstandingItem) =>
            item.sku === product.sku || product.skuMaps?.includes(item.sku)
          );
          let outstandingQty = outstandingItem?.total || 0;
          
          const totalTop3Quantity = (inventoryItem.top3Shop ?? 0) + (inventoryItem.top3Amz ?? 0);

          // Initialize all required GridDataItem properties
          const newAvg = totalTop3Quantity + (outstandingQty / 3);
          const projectedQuantity = Math.round(newAvg * monthsProjection);
          const monthsRemainingOtw = Math.round(((inventoryItem.otw ?? 0) + (inventoryItem.warehouse ?? 0) + (inventoryItem.inventoryAmazon ?? 0)) / newAvg * 10) / 10;
          const monthsRemaining = Math.round(((inventoryItem.warehouse ?? 0) + (inventoryItem.inventoryAmazon ?? 0)) / newAvg * 10) / 10;
          const quantityNeeded = Math.max(0, projectedQuantity - (inventoryItem.warehouse ?? 0) - (inventoryItem.inventoryAmazon ?? 0) - (inventoryItem.otw ?? 0));
          const casesNeeded = quantityNeeded < 120 ? 0 : Math.ceil(quantityNeeded / 12);
          
          let initialNewOrder: number | undefined = undefined;
          if (inProgressPurchase && inProgressPurchase.items) {
            const inProgressItem = inProgressPurchase.items.find(item => item.sku === product.sku);
            if (inProgressItem) {
              // Convert stored units back to cases for display (stored units ÷ 12)
              initialNewOrder = Math.round(inProgressItem.qty / 12);
            }
          }

          rawData.push({
            sku: product.sku,
            outstanding: outstandingQty,
            top3Shop: inventoryItem.top3Shop ?? 0,
            top3Amz: inventoryItem.top3Amz ?? 0,
            last_30_days: inventoryItem.last_30_days ?? 0,
            otw: inventoryItem.otw ?? 0,
            warehouse: inventoryItem.warehouse ?? 0,
            inventoryAmazon: inventoryItem.inventoryAmazon ?? 0,
            projectedQuantity,
            monthsRemainingOtw,
            monthsRemaining,
            quantityNeeded,
            casesNeeded,
            unitsPerCase: 12, // Hardcode as per user feedback
            newOrder: initialNewOrder
          });
        }

        // Find invoices and outstanding items with no matching product
        for (const outstandingItem of outstanding?.items || []) {
          if (!productsResponse.products.find((product: Product) =>
            product.sku === outstandingItem.sku || product.skuMaps?.includes(outstandingItem.sku)
          )) {
            missingColorsList.push(`Outstanding item with no product ${outstandingItem.sku}`);
          }
        }

        for (const i of inventoryData) {
          if (!productsResponse.products.find((product: Product) =>
            product.sku === i.color || product.skuMaps?.includes(i.color ?? "")
          )) {
            missingColorsList.push(`Inventory item with no product ${i.color} - ${(i.top3Shop ?? 0) + (i.top3Amz ?? 0)}`);
          }
        }

        setMissingColors(missingColorsList);
        setRawData(rawData);
        setProducts(productsResponse.products);
        toast.success('Data loaded successfully');
      } catch (error) {
        console.error('Error loading projections data:', error);
        // The specific error toast will already have been shown by the individual catch blocks
        setGridData([]); // Set empty grid data to avoid showing stale data
      }
    }

    getData();
  }, [sixMonthsAgo, monthsProjection]);

  // Update grid data when monthsProjection or rawData changes
  useEffect(() => {
    if (!rawData) return;

    // Sort data by total top3Quantity (Shop + Amz) in descending order and add ranks
    const sortedData = [...rawData].sort((a, b) => ((b.top3Shop ?? 0) + (b.top3Amz ?? 0)) - ((a.top3Shop ?? 0) + (a.top3Amz ?? 0)));
    const totalItems = sortedData.length;
    const newGridData = sortedData.map((item, index) => {
      const totalTop3 = (item.top3Shop ?? 0) + (item.top3Amz ?? 0);
      const newAvg = totalTop3 + (item.outstanding / 3);
      const projectedQuantity = Math.round(newAvg * monthsProjection);
      const monthsRemainingOtw = Math.round(((item.otw ?? 0) + (item.warehouse ?? 0) + (item.inventoryAmazon ?? 0)) / newAvg * 10) / 10;
      const monthsRemaining = Math.round(((item.warehouse ?? 0) + (item.inventoryAmazon ?? 0)) / newAvg * 10) / 10;
      const quantityNeeded = Math.max(0, projectedQuantity - (item.warehouse ?? 0) - (item.inventoryAmazon ?? 0) - (item.otw ?? 0));
      const casesNeeded = quantityNeeded < 120 ? 0 : Math.ceil(quantityNeeded / 12);

      // Calculate rank on a 1-100 scale
      const percentileRank = index / (totalItems - 1);
      const rank = Math.round(percentileRank * 99 + 1); // Maps 0 to 1 and 1 to 100

      return {
        ...item,
        rank,
        projectedQuantity,
        monthsRemainingOtw,
        monthsRemaining,
        quantityNeeded,
        casesNeeded,
        unitsPerCase: 12 // Ensure it's also set here when recalculating gridData
      };
    });

    setGridData(newGridData);
  }, [monthsProjection, rawData]);

  const syncInProgressPurchaseToFirebase = useCallback(async (dataToSync?: GridDataItem[]) => {
    const dataSource = dataToSync || gridData;
    if (!dataSource) return;

    const itemsToSave: PurchaseItemDetail[] = dataSource
      .filter(item => item.newOrder && item.newOrder > 0)
      .map(item => ({
        sku: item.sku,
        productId: item.sku, // Assuming sku is the productId for purchases
        quantityInCases: item.newOrder!,
        quantityInUnits: item.newOrder! * item.unitsPerCase, // Convert cases to units for storage
      }));

    try {
      const result = await saveInProgressPurchase(itemsToSave);
      if (!result.success) {
        toast.error(result.message || 'Failed to sync in-progress purchase.');
      } else {
        // console.log('In-progress purchase synced to Firebase.');
      }
    } catch (error) {
      toast.error(`Error syncing in-progress purchase: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [gridData]);
  
  const debouncedSyncInProgressPurchase = useMemo(() => {
    // Basic debounce implementation
    let timeoutId: NodeJS.Timeout;
    return (dataToSync?: GridDataItem[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        syncInProgressPurchaseToFirebase(dataToSync);
      }, 1000); // Debounce by 1 second
    };
  }, [syncInProgressPurchaseToFirebase]); // Recreate if gridData reference changes, though its content is what matters

  // Update valueSetter to call debouncedSync
  useEffect(() => {
    setColumnDefs(prevDefs =>
      prevDefs.map(colDef => {
        if (colDef.field === "newOrder") {
          return {
            ...colDef,
            valueSetter: (params: ValueSetterParams<GridDataItem, number | string | undefined | null>) => {
              const editorValue = params.newValue;
              if (editorValue === null || editorValue === undefined || editorValue === '') {
                params.data.newOrder = undefined;
              } else {
                const val = Number(editorValue);
                params.data.newOrder = isNaN(val) ? undefined : Math.floor(val);
              }
              debouncedSyncInProgressPurchase(); // Call debounced sync
              return true;
            },
          };
        }
        return colDef;
      })
    );
  }, [debouncedSyncInProgressPurchase]);


  const handleFillNewOrder = () => {
    if (!gridData) return;
    const updatedGridData = gridData.map(item => ({
      ...item,
      newOrder: item.casesNeeded,
    }));
    setGridData(updatedGridData);
    if (gridRef.current && gridRef.current.api) {
        gridRef.current.api.refreshCells();
    }
    // Pass the updated data directly to sync to avoid stale state issues
    syncInProgressPurchaseToFirebase(updatedGridData);
    toast.success("'New Order' column filled from 'Cases Needed'.");
  };

  const handleClearNewOrder = () => {
    if (!gridData) return;
    const updatedGridData = gridData.map(item => ({
      ...item,
      newOrder: undefined,
    }));
    setGridData(updatedGridData);
    if (gridRef.current && gridRef.current.api) {
        gridRef.current.api.refreshCells();
    }
    // Pass the updated data directly to sync to avoid stale state issues
    syncInProgressPurchaseToFirebase(updatedGridData);
    toast.info("'New Order' column cleared.");
  };

  const handleCreatePurchaseEntry = async () => {
    // 1. Check if there's an in-progress purchase to formalize
    const inProgressPurchase = await getInProgressPurchase();
    if (!inProgressPurchase || !inProgressPurchase.items || inProgressPurchase.items.length === 0) {
      toast.info("No 'in-progress' purchase items to create a formal purchase.");
      return;
    }

    // 2. Prompt user for the new Purchase ID
    const newPurchaseId = window.prompt("Enter the new Purchase ID for this order:");
    if (!newPurchaseId || newPurchaseId.trim() === "") {
      toast.info("Purchase creation cancelled: No Purchase ID entered."); // Changed from toast.warn
      return;
    }

    // 3. Call the server action to convert
    try {
      const result = await convertInProgressToFormalPurchase(newPurchaseId.trim());
      if (result.success && result.purchaseId) {
        toast.success(`Purchase created successfully with ID: ${result.purchaseId}`);
        // Clear the local grid's "New Order" column and sync (which deletes the in-progress Firebase doc)
        
        // Create a new version of gridData with newOrder cleared
        const clearedGridData = gridData?.map(item => ({
          ...item,
          newOrder: undefined,
        })) || [];
        setGridData(clearedGridData);
        
        // Explicitly call sync with an empty array to ensure the in-progress doc is deleted
        // This is because handleClearNewOrder itself relies on the current gridData state
        // which might not have updated yet if setGridData is async.
        await saveInProgressPurchase([]); // Ensure in-progress is cleared from DB

        if (gridRef.current && gridRef.current.api) {
          gridRef.current.api.refreshCells(); // Refresh grid to show cleared newOrder
        }

      } else {
        throw new Error(result.message || "Failed to create formal purchase.");
      }
    } catch (error) {
      console.error("Failed to create formal purchase:", error);
      toast.error(`Failed to create purchase: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
  <main className="flex flex-1 flex-col items-center p-4">
    {missingColors.length > 0 && (
    <Accordion>
    <AccordionItem key="1" aria-label="BQ Top Colors missing products:" title="BQ Top Colors missing products:">
       {missingColors.map((color, i) => (
         <div key={i}>{color}</div>
       ))}
    </AccordionItem>
    </Accordion>
    )}
    <div className="text-sm text-gray-600">{recentDates}</div>
    <div className="text-sm text-gray-600">From: {sixMonthsAgo.toLocaleDateString()}</div>
    
    <div className="w-full flex justify-between items-center my-4">
      <div className="w-full max-w-xs">
        <Input
          type="number"
          label="Months Projection"
          value={monthsProjection.toString()}
          onChange={(e) => setMonthsProjection(parseInt(e.target.value) || DEFAULT_MONTHS_PROJECTION)}
          min={1}
        />
      </div>
      <div className="flex gap-2">
        <Button color="primary" onClick={handleFillNewOrder}>Fill New Order</Button>
        <Button color="warning" onClick={handleClearNewOrder}>Clear New Order</Button>
        <Button color="success" onClick={handleCreatePurchaseEntry}>Create Purchase</Button>
      </div>
    </div>

    <div className="flex flex-1 w-full ag-theme-alpine" style={{ height: 'calc(100vh - 280px)' }}> {/* Adjusted height */}
      <CcGrid
        ref={gridRef}
        rowData={gridData || []}
        colDefs={columnDefs}
        setColDefs={setColumnDefs}
        additionalOptions={{
          defaultColDef,
          suppressColumnVirtualisation: true,
          enableCellTextSelection: true,
        }}
      />
    </div>
  </main>
  )
}

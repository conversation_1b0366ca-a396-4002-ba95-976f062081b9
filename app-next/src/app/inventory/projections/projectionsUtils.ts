"use client";

import sharedFirestoreClientInstance from '~/data/google/firebase-client';
import { FilamentPurchaseService, PurchaseData, PurchaseItemData, CreatePurchaseData, CreatePurchaseItemData } from '~/services/FilamentPurchaseService';

// Define additional types needed for the projections functionality
interface PurchaseItemDetail {
  sku: string;
  productId: string;
  quantityInCases: number;
  quantityInUnits: number;
  unitCost?: number;
  caseCost?: number;
  unitsPerCase?: number;
}

// Initialize FilamentPurchaseService (uses the shared FirebaseLib instance)
// Note: FilamentPurchaseService still expects FirebaseLib interface
// This will need to be updated in a separate refactoring task
const filamentPurchaseService = new FilamentPurchaseService(sharedFirestoreClientInstance as any);

const IN_PROGRESS_PURCHASE_ID = 'in-progress';

export async function saveInProgressPurchase(itemsToSave: PurchaseItemDetail[]): Promise<{ success: boolean, message?: string }> {
  try {
    let inProgressPurchaseHeader = await filamentPurchaseService.getPurchase(IN_PROGRESS_PURCHASE_ID);

    if (!inProgressPurchaseHeader) {
      // Create a new in-progress purchase document
      const newPurchaseData: CreatePurchaseData = {
        invoiceDate: new Date(),
        'on-the-way': false,
        ETA: new Date(),
        quantity: 0,
        productCost: 0,
        shippingCost: 0,
        totalCost: 0,
        notes: 'In-progress purchase draft'
      };

      const result = await filamentPurchaseService.createPurchase(newPurchaseData);
      if (!result.success) {
        throw new Error(result.error || 'Failed to create in-progress purchase');
      }
      
      // Delete the auto-generated purchase and create one with our specific ID
      if (result.id) {
        await filamentPurchaseService.deletePurchase(result.id);
      }
      
      // Use service to create purchase with specific ID
      const createResult = await filamentPurchaseService.createPurchaseWithId(IN_PROGRESS_PURCHASE_ID, newPurchaseData);
      if (!createResult.success) {
        throw new Error(createResult.error || 'Failed to create in-progress purchase with specific ID');
      }
      console.log(`Created in-progress purchase header: ${IN_PROGRESS_PURCHASE_ID}`);
    } else {
      console.log(`In-progress purchase header already exists: ${IN_PROGRESS_PURCHASE_ID}`);
    }

    // Delete all existing items for this purchase
    const existingItems = await filamentPurchaseService.getPurchaseItems(IN_PROGRESS_PURCHASE_ID);
    for (const item of existingItems) {
      await filamentPurchaseService.deletePurchaseItem(IN_PROGRESS_PURCHASE_ID, item.id);
    }
    console.log(`Deleted ${existingItems.length} existing items from ${IN_PROGRESS_PURCHASE_ID}`);

    // Add new items if any
    if (itemsToSave && itemsToSave.length > 0) {
      for (const item of itemsToSave) {
        let unitPrice = item.unitCost || 0;
        if (!unitPrice && item.caseCost && item.unitsPerCase && item.unitsPerCase > 0) {
          unitPrice = item.caseCost / item.unitsPerCase;
        }
        
        const newItemData: CreatePurchaseItemData = {
          sku: item.sku,
          qty: item.quantityInUnits,
          unitPrice: unitPrice,
          price: unitPrice * item.quantityInUnits,
          description: `Product ${item.sku}`,
          model: item.sku,
          itemNo: item.sku
        };
        
        await filamentPurchaseService.createPurchaseItem(IN_PROGRESS_PURCHASE_ID, newItemData);
      }
      console.log(`Added ${itemsToSave.length} items to ${IN_PROGRESS_PURCHASE_ID}`);
    }

    return { success: true };
  } catch (error: any) {
    console.error('Error in saveInProgressPurchase:', error);
    return { success: false, message: error.message || 'An unknown error occurred while saving in-progress purchase.' };
  }
}

export async function getInProgressPurchase(): Promise<(PurchaseData & { items: PurchaseItemData[] }) | null> {
  try {
    const purchase = await filamentPurchaseService.getPurchaseWithItems(IN_PROGRESS_PURCHASE_ID);
    return purchase;
  } catch (error: any) {
    if (error.message?.includes('No such document') || error.code === 'not-found' || error.message?.includes('not found')) {
        console.log(`In-progress purchase '${IN_PROGRESS_PURCHASE_ID}' not found.`);
        return null;
    }
    console.error('Error in getInProgressPurchase:', error);
    return null; 
  }
}

export async function convertInProgressToFormalPurchase(newPurchaseId: string): Promise<{ success: boolean, purchaseId?: string, message?: string }> {
  if (!newPurchaseId || newPurchaseId.trim() === '') {
    return { success: false, message: 'New Purchase ID cannot be empty.' };
  }
  const trimmedNewPurchaseId = newPurchaseId.trim();

  try {
    const inProgressPurchase = await getInProgressPurchase();

    if (!inProgressPurchase) {
      return { success: false, message: `In-progress purchase '${IN_PROGRESS_PURCHASE_ID}' not found.` };
    }

    if (!inProgressPurchase.items || inProgressPurchase.items.length === 0) {
      return { success: false, message: `In-progress purchase '${IN_PROGRESS_PURCHASE_ID}' has no items to convert.` };
    }

    // Calculate totals from items
    const totals = filamentPurchaseService.calculatePurchaseTotals(inProgressPurchase.items);

    // Prepare data for the new formal purchase
    const newPurchaseData: CreatePurchaseData = {
      invoiceDate: new Date(),
      'on-the-way': true, // Set as on-the-way since it's a new purchase order
      ETA: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Default ETA 30 days from now
      quantity: totals.totalQuantity,
      productCost: totals.totalItemCost,
      shippingCost: 0, // Default shipping cost
      totalCost: totals.totalItemCost, // Will be updated when shipping is added
      notes: `Converted from in-progress purchase on ${new Date().toISOString()}`
    };

    // Create the formal purchase with a specific ID using service
    const createResult = await filamentPurchaseService.createPurchaseWithId(trimmedNewPurchaseId, newPurchaseData);
    if (!createResult.success) {
      throw new Error(createResult.error || 'Failed to create formal purchase with specific ID');
    }
    console.log(`Created new formal purchase header: ${trimmedNewPurchaseId}`);

    // Copy items to the new purchase
    for (const item of inProgressPurchase.items) {
      const newItemData: CreatePurchaseItemData = {
        qty: item.qty,
        sku: item.sku,
        unitPrice: item.unitPrice,
        price: item.price,
        description: item.description,
        model: item.model,
        itemNo: item.itemNo
      };
      await filamentPurchaseService.createPurchaseItem(trimmedNewPurchaseId, newItemData);
    }
    console.log(`Copied ${inProgressPurchase.items.length} items to ${trimmedNewPurchaseId}`);

    // Clear items from in-progress purchase
    for (const item of inProgressPurchase.items) {
      await filamentPurchaseService.deletePurchaseItem(IN_PROGRESS_PURCHASE_ID, item.id);
    }
    console.log(`Cleared items from ${IN_PROGRESS_PURCHASE_ID}`);

    // Update the in-progress purchase notes to indicate conversion
    await filamentPurchaseService.updatePurchase(IN_PROGRESS_PURCHASE_ID, {
      notes: `Converted to formal purchase ${trimmedNewPurchaseId} on ${new Date().toISOString()}`
    });
    console.log(`Updated notes of ${IN_PROGRESS_PURCHASE_ID} to indicate conversion`);

    return { success: true, purchaseId: trimmedNewPurchaseId };
  } catch (error: any) {
    console.error('Error in convertInProgressToFormalPurchase:', error);
    return { success: false, message: error.message || 'An unknown error occurred during conversion.' };
  }
}

// Helper function to get purchases on the way (useful for projections)
export async function getPurchasesOnTheWay(): Promise<PurchaseData[]> {
  try {
    return await filamentPurchaseService.getPurchasesOnTheWay();
  } catch (error: any) {
    console.error('Error getting purchases on the way:', error);
    throw new Error(`Failed to get purchases on the way: ${error.message || 'Unknown error'}`);
  }
}

// Helper function to get all purchases with items (useful for detailed projections)
export async function getAllPurchasesWithItems(): Promise<(PurchaseData & { items: PurchaseItemData[] })[]> {
  try {
    return await filamentPurchaseService.getAllPurchasesWithItems();
  } catch (error: any) {
    console.error('Error getting all purchases with items:', error);
    throw new Error(`Failed to get all purchases with items: ${error.message || 'Unknown error'}`);
  }
}
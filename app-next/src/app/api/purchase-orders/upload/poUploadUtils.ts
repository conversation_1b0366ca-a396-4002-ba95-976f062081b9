import { writeFile } from 'fs/promises'
// import { runTextractToFirebase } from '~/data/aws/textract-to-firebase'
import { generateContent } from './gemini'
import { tmpdir } from 'os'
import { FirestoreRestService } from '~/data/google/firestore/FirestoreRestService'
import { Products } from '~/data/google/products'

export async function processPurchaseOrderUpload(data: Buffer, extractMethod: string, idToken: string) {
  // if (!file) {
  //   throw new Error('No file provided')
  // }

  try {
    const firestoreClient = new FirestoreRestService(idToken);
    let lookupTable = await getLookupTable(firestoreClient);

    if (extractMethod === 'gemini') {
      let result = await generateContent(data, lookupTable);
      await saveToFirestore(result, firestoreClient)
      return { success: true, results: result }
    }

    // let result = await runTextractToFirebase(file)
    // if (result.error) {
    //   console.error("ERROR", result.error)
    //   return { success: false, error: JSON.stringify(result.error), tables: result.tables }
    // }
    // return { success: true, results: result }
  }
  catch (error) {
    console.error("ERROR", error)
    return { success: false, error: error }
  }
}

export async function saveToFirestore(poData: {
  poNumber: string,
  poDate: Date,  
  items: [
    {
     sku: string,
     quantity: number,
     amount: number
    }
  ]
  }, firestoreClient: FirestoreRestService
) {
  console.log(`Saving PO Number: ${poData.poNumber}, PO Date: ${poData.poDate}`)
  // const poDoc = firestoreClient.getDoc('purchase-orders', poData.poNumber, idToken);
  const poResult =  await firestoreClient.set('purchase-orders', poData.poNumber, { 
    'po-number': poData.poNumber,
    'po-date': new Date(poData.poDate),
    'status': 'New'
  });
  console.log(poResult);
  
  for (let poItem of poData.items) {
    console.log(`Saving PO Item: ${poData.poNumber}, ${poItem.sku}, ${poItem.quantity}, ${poItem.amount}`)
    const poItemDoc = firestoreClient.set(`purchase-orders/${poData.poNumber}/purchase-order-items`, 
      poItem.sku,
      poItem);
  }
}

async function getLookupTable(firestoreClient: FirestoreRestService) {
  const products = new Products(firestoreClient);
  await products.init();
  const lookupTable = products.products.map(product => product.sku);
  return lookupTable;
}

  
import  {GoogleGenerativeAI, SchemaType, ResponseSchema }  from "@google/generative-ai"
import { parse } from 'csv-parse/sync';

const schema: ResponseSchema = {
  description: 'A JSON object containing the extracted data from the invoice',
  type: SchemaType.OBJECT,
  properties: {
    poNumber: {
      description: 'The PO Number (Required)',
      type: SchemaType.STRING,
      nullable: false,
    },
    poDate: {
      description: 'The Ordered Date (Required)',
      type: SchemaType.STRING,
      nullable: false,
    },
    poTotal: {
      description: 'Total Amount (a number with two decimals no comma, Required)',
      type: SchemaType.STRING,
    },
    items: {
      description: `(Required) An string in CSV format as follows: style,name,sku,cost,quantity',
      eg: PLA-ORANGE-SORB,orange sorbet,pla-orange-sorbet,19.77,10`,
      type: SchemaType.STRING,
    },
    errors: {
      description: 'Any errors that occurred during the extraction process',
      type: SchemaType.STRING,
    },
  },
  example: {
    poNumber: '55924',
    poDate: '10/16/24',
    poTotal: '99999.99',
    items: `style,name,sku,cost,quantity',
    PLA-ORANGE-SORB,orange sorbet,pla-orange-sorbet,19.77,10`,
    errors: ''
  }
}
  

const prompt = (lookupTable: string[]) => `
You are a data extraction API that can read tabular data from invoice image files and generate JSON from them. You will have access to these files. The output format should match the following, only output json, with no markdown formatting or wrappers. This is very important as the response will be processed by a computer not a human.
{
poNumber: [located in the top left on every page under the text "Purchase Order"]
poDate: [located in the "ordered" field on the top left of every page
poTotal: [located on the last page]
items: "style,name,sku,cost,quantity
[... csv data eg: PLA-ORANGE-SORB,orange sorbet,pla-orange-sorbet,19.77,10]
",
errors:
}
All prices and total should contain only a valid number, remove US$
Use the following sku lookup table to get correct skus in case they are cut off or misread
Lookup table:
[
${lookupTable.join(',\n')}
]

It is crucial that you do not wrap with markdown or include \`\`\`json or similar in the response. `


export async function generateContent(fileData: Buffer, lookupTable: string[]) {
  const apiKey = process.env.GOOGLE_GEMINI_API_KEY!;

  
  const genAI = new GoogleGenerativeAI(apiKey);
  const model = genAI.getGenerativeModel({ 
    model: "gemini-2.5-pro-preview-05-06", 
    generationConfig: {
      responseMimeType: "application/json",
      // responseSchema: schema,
    },  
  });
  console.log("Generating content")
  const result = await model.generateContent([
    prompt(lookupTable),
    {
      inlineData: {
        mimeType: "application/pdf",
        data: fileData.toString('base64')
      }
    },
  ]);
  let resultText = result.response.text();
  console.log("Response", resultText);

  let jsonResults = await parseResults(resultText);
  return jsonResults;
}

async function parseResults(results: string) {
  let jsonResults = JSON.parse(results);
  console.log({jsonResults});
  let itemsCsv = jsonResults.items;
  let jsonData = await parse(itemsCsv, {columns: true});
  let parsedItems = jsonData.map((item: any) => ({
    sku: item.sku,
    quantity: parseInt(item.quantity),
    amount: parseFloat(item.cost)
  }));
  console.log({parsedItems});
  jsonResults.items = parsedItems;
  return jsonResults; 
}

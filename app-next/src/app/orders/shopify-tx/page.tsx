"use client"

import { useState, useEffect, useMemo } from 'react';
import { useSearchParams, useRouter, usePathname} from 'next/navigation'
import CcGrid from '~/components/cc-grid';
import { 
  ICellRendererParams, 
  CellValueChangedEvent,
  ValueFormatterParams,
  ValueSetterParams,
  ValueGetterParams,
  ColDef
} from "ag-grid-community";
import firestoreClient from '~/data/google/firebase-client';
import { Accordion, AccordionItem } from "@nextui-org/react";
import { toast } from 'sonner';
import { fromShopifyGid } from "~/data/shopify/shopifyUtils";

interface Transaction {
  gateway: string;
  status: string;
  kind: string;
  amountSet: {
    presentmentMoney: {
      amount: string;
    };
  };
  [key: string]: any;
}

interface Order {
  id: string;
  orderId: string;
  name: string;
  currentTotalPriceSet: {
    shopMoney: {
      amount: string;
    };
  };
  transactions: Transaction[];
  [key: string]: any;
}

export default function Page() {
  const [columnDefs, setColumnDefs] = useState<ColDef[]>([
    { 
      field: 'processedAt',
      headerName: 'Processed At',
      valueGetter: (params: ValueGetterParams) => new Date(params.data.processedAt),
      cellDataType: 'date',
    },
    { 
      field: 'createdAt',
      headerName: 'Created At',
      valueGetter: (params: ValueGetterParams) => new Date(params.data.createdAt),
      cellDataType: 'date',
    },
    { field: 'ord_id',
      headerName: 'Order ID',
      valueGetter: (params: ValueGetterParams) => parseInt(params.data.ord_id),
      cellRenderer: (params: ICellRendererParams) => (<a href={`https://admin.shopify.com/store/lilmisscakes/orders/${fromShopifyGid(params?.data?.ord_orderId)}`} target="_blank" rel="noopener">{params.value}</a>) },
    { field: 'ord_name',
      headerName: 'Order Name',
    },
    { field: 'ord_amount',
      headerName: 'Order Amount',
      valueGetter: (params: ValueGetterParams) => !params.node?.rowPinned ? parseFloat(params.data?.ord_currentTotalPriceSet?.shopMoney?.amount) || 0 : params.data.amount ,
      cellDataType: 'number',
    },
    { field: 'tx_amount',
      headerName: 'Tx Amount',
      valueGetter: (params: ValueGetterParams) => parseFloat(params.data?.amountSet?.presentmentMoney?.amount) || 0,
      cellDataType: 'number',
    },
    { field: 'gateway',
      headerName: 'Gateway',
    },
  ]);

  const [gridData, setGridData] = useState<any[]>();
  const [missingColors, setMissingColors] = useState<string[]>([]);
  type Product = {
    sku: string;
    [key: string]: any;
  };
  const [products, setProducts] = useState<[Product?]>([]);

  useEffect(() => {
    async function getData() {
      try {
        firestoreClient.firestore
        const dataPromise = firestoreClient.queryCollection<Order>('shopify-orders-tx', [
          ["cancelledAt", "==", null],
          ["processedAt", ">=", "2024-01-01"]
        ]);
        let orders = await dataPromise;

        let data: any[] = [];
        orders.forEach(order => {
          // Add all transactions that match criteria
          const transactions = order.transactions.filter((tx: any) =>
            tx.gateway === 'manual' && tx.status === 'SUCCESS' && tx.kind === 'SALE'
          );
          
          transactions.forEach((tx: any) => {
            data.push({
              ...tx,
              // Add order fields prefixed with ord_
              ord_id: order.id,
              ord_orderId: order.orderId,
              ord_name: order.name,
              ord_currentTotalPriceSet: order.currentTotalPriceSet, // Keep full price set for valueGetter
            });
          });
        });

        console.log({ data });
     
        setGridData(data);
        toast.success('Orders loaded successfully');
      } catch (error) {
        console.error('Error loading orders:', error);
        toast.error('Failed to load orders: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    }

    getData();
  }, []);

  return (
    <main className="flex flex-1 flex-col items-center">  
      {/* {missingColors.length > 0 && (
      <Accordion>
      <AccordionItem key="1" aria-label="BQ Top Colors missing products:" title="BQ Top Colors missing products:">
        {missingColors.map((color, i) => (
          <div key={i}>{color}</div>
        ))}
      </AccordionItem>
      </Accordion>
      )} */}
      <div className="flex flex-1 w-full mt-10" style={{ height: 'calc(100% - 1000px)' }}>
        <CcGrid 
          rowData = { gridData || [] } 
          colDefs={columnDefs} 
          setColDefs={setColumnDefs}
          autoAddMissingColumns={true}
          additionalOptions={{
            // suppressColumnVirtualisation: true,
            // suppressRowVirtualisation: true,
            domLayout: 'normal',
          }}
        />
      </div>
    </main>
  )
}
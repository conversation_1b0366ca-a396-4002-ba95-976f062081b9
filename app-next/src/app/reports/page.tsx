"use client"

import { useState, useEffect, useMemo } from 'react';
import CcGrid from '~/components/cc-grid'
import { 
  ICellRendererParams, 
  CellValueChangedEvent,
  ValueFormatterParams,
  ValueSetterParams,
  ColDef,
  ValueGetterParams,
  ValueParserParams
} from "ag-grid-community";
import { Button } from '@nextui-org/react'

import firestoreClient from '~/data/google/firebase-client';
import { ReportItem } from '~/data/google/firestore-types';

import { useAuth } from "../../auth/context";
import { saveLatestRestockReport } from '~/data/amazon/amazon-seller-firestore';
import { generateManifestFile } from '~/data/amazon/amzseller-send-to-amazon';
import path from 'path';

export default function Page() {
  const { user } = useAuth();

  const [rowData, setRowData] = useState<any[]>([]);
  const [missingAmazonMap, setMissingAmazonMap] = useState<string>('');
  const [reportEndTime, setReportEndTime] = useState<string>('');

  const valueToNumberGetter = (params: ValueGetterParams) => parseInt(params.data[params.colDef.field!]) || 0;
  const colDefs: ColDef[] = [
    {
      field: "qtyToSend",
      editable: true,
      cellDataType: 'number',
    },
    {
      field: "warehouseInventory",
      valueGetter: valueToNumberGetter
    },
    {
      field: "recommendedReplenishmentQty",
      valueGetter: valueToNumberGetter
    },
    {
      field: "available",
      cellDataType: 'string',
    },
    {
      field: "daysOfSupplyAtAmazonFulfillmentNetwork",
      valueGetter: valueToNumberGetter
    },
    {
      field: "inbound",
      cellDataType: 'number',
    },
    {
      field: "merchantSKU",
      cellDataType: 'string',
    },
    {
      field: "Create shipping plan",
      cellDataType: 'string',
    },
    {
      field: "recommendedShipDate",
      cellDataType: 'string',
    },
    {
      field: "totalDaysOfSupplyIncludingUnitsFromOpenShipments",
      valueGetter: valueToNumberGetter
    },
    {
      field: "totalUnits",
      valueGetter: valueToNumberGetter
    },
  ];
  const [columnDefs, setColumnDefs] = useState<ColDef[]>(colDefs);
  const defaultColDef = {
    valueGetter: (params: ValueGetterParams) => {
      let value = params.data[params.colDef.field!];
      //if value is a number, return it as a number
      return isNaN(value) ? value : Number(value);
    }
  }


  useEffect(() => {
    type ReportItemCustom = ReportItem & { qtyToSend?: number };
    async function getData() {
      function calculateQtyToSend(report: any) {
        report.forEach((r: any) => {
          const toCaseUp = (qty: number) => Math.ceil(qty / 12) * 12;
          const toCaseDown = (qty: number) => Math.floor(qty / 12) * 12;
          r.qtyToSend = Math.min(toCaseDown(r.warehouseInventory), toCaseUp(r.recommendedReplenishmentQty));
        });
      }

      let reportPromise = firestoreClient.getRestockReport();
      let productsPromise = fetch('api/products?include-warehouse').then(response => response.json());
      let [reportResult, productsResult] = await Promise.all([reportPromise, productsPromise]);
      console.log({reportResult, productsResult})
      let report = reportResult.reportData;
      setReportEndTime(reportResult.report.processingEndTime);

      const products = productsResult.products;
      //Add amazon data to products
      let data = products.map((product: any) => {
        const reportItem = report.find((p) => p.merchantSKU?.toLowerCase() === product.amzData?.sellerSku?.toLowerCase());
        return {
          ...reportItem,
          ...product,
        }
      });
      //Add qtyToSend to data
      calculateQtyToSend(data);

      //Filter irrelevant amazon products
      data = data.filter((i: any) => 
        !(i.cookiecadSku == 'inactive') &&
        !(i.warehouseInventory < 36)
        );

      let missingAmazonItems = report.filter(reportItem => !products.some((w: any) => w.amzData?.sellerSku?.toLowerCase() === reportItem.merchantSKU?.toLowerCase()));   
      
      setMissingAmazonMap(missingAmazonItems.map(i => i.merchantSKU).join(', '));
      setRowData(data);
    }

    if (user) {
      console.log('user', user)

      getData()
        .catch(error => console.error('Error:', error));
      

    }
  }, [user]);

  const getLatestReportClick = async () => {
    saveLatestRestockReport().then(result => {
      console.log(result);
    });
  }

  const downloadManifestClick = async () => {
    const inventoryData = rowData.reduce((acc: any[], item: any) => {
      if (item.qtyToSend > 0) {
        acc.push({
          'Merchant SKU': item.merchantSKU,
          'Quantity': item.qtyToSend,
          'Number of boxes': item.qtyToSend / 12
        })
      }
      return acc;
    }, [])
    //This is to work around a bug in server actions - including this here, will tell next to include it in the build
    // let tsvData = await generateManifestFile(inventoryData);
    let tsvData = await fetch('/api/amazon', {
      method: 'POST',
      body: JSON.stringify(inventoryData)
    }).then(response => response.text());
    let blob = new Blob([tsvData], { type: 'text/plain' });
    let url = window.URL.createObjectURL(blob);
    let a = document.createElement('a');
    a.href = url;
    let currentDateFileSafe = new Date().toISOString().split('T')[0];
    a.download = `manifest-${currentDateFileSafe}.txt`;
    a.click();  

  }

  
  return (
  <main className="flex flex-1 flex-col items-center">  
    { !user && <div>Please log in...</div>}

    {missingAmazonMap.length > 0 && (
      <div>Amazon items missing mapping: {missingAmazonMap}</div>
    )}
    {reportEndTime.length > 0 && (
      <div>Report End Time: {reportEndTime}</div> )}

    <div className="flex flex-1 w-full mt-10" style={{ height: 'calc(100% - 1000px)' }}>
      <CcGrid 
        rowData = { rowData } 
        colDefs={columnDefs} 
        setColDefs={setColumnDefs}
        autoAddMissingColumns={true}
        additionalOptions={{
          sideBar: 'columns',
          domLayout: 'normal',  
          defaultColDef: defaultColDef,
        }}
        headerItems={
          <>
            <Button onClick={getLatestReportClick}>Get Latest Report</Button>
            <Button onClick={downloadManifestClick}>Download Manifest File</Button>
          </>
        }
      />
    </div>
  </main>
  )
}
"use client";

import { useState, useEffect, useRef, useMemo } from "react";
import { AgGridReact } from "ag-grid-react";
import CcGrid from "~/components/cc-grid";
import "ag-grid-community/styles/ag-grid.css"; // Core CSS
import "ag-grid-community/styles/ag-theme-balham.css"; // Theme
import { 
  ICellRendererParams, 
  CellValueChangedEvent,
  ValueFormatterParams,
  ValueSetterParams,
  ColDef,
  RowValueChangedEvent
} from "ag-grid-community";
import firestoreClient from '~/data/google/firebase-client'
import { Timestamp } from "firebase/firestore";
import { useRouter } from 'next/navigation'


import { useSearchParams } from "next/navigation";
import {shopifyOrderMonthlyReport} from "~/data/google/firstore-shopify";

export default function Page({ params }: { params: { poId: string } }) {
  const [rowData, setRowData] = useState([] as any[]);

  //Load data
  useEffect(() => {
    firestoreClient.get<shopifyOrderMonthlyReport>('data', 'shopifyOrdersMonthly').then((data) => {
      console.log("shopifyOrderMonthly", data);
      if (!data) {
        console.warn("No shopify order monthly data found");
        return;
      }
      let rows = [];
      for (const [yearMonth, value] of Object.entries(data)) {
        for (const [sku, skuValue] of Object.entries(value.skus)) {
          rows.push({
            yearMonth,
            sku,
            ...skuValue
          });
        }
      }
      setRowData(rows);
    });
  }, []);

  const [colDefs, setColDefs] = useState<ColDef[]>([
  ]);
  return (
    <main className="flex flex-1 flex-col items-center">  
      <div className="flex flex-1 w-full mt-10" style={{ height: 'calc(100% - 1000px)' }}>
        <CcGrid
          rowData={rowData}
          colDefs={colDefs}
          setColDefs={setColDefs}
          autoAddMissingColumns={true}
          additionalOptions= {{
            enableCellTextSelection:true,
            suppressColumnVirtualisation: true,
            suppressRowVirtualisation: true,
            domLayout: 'normal',  

          }}
        />
      </div>

    </main>
  );
}

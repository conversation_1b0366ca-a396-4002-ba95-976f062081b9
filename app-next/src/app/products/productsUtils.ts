"use server"
import {importListingsToFirestore} from '~/data/google/amazonToFirestore'
import {marketplaceIds } from '~/data/amazon/amazon-seller'
import { shopifyNode } from 'cookiecad-shared'
import firebase from "~/data/google/firebase-server";
import { getProductsByFilamentType } from '~/data/shopify/shopifyGrapql';

interface Product {
  id: string;
  skuMaps?: string[];
  [key: string]: any;
}

const filamentCollection = 224410534048;

// export async function uploadFile(formData: FormData) {
//   const file: any = formData.get('file');

//   await (firebase.firestore as Firestore).recursiveDelete(
//     (firebase.firestore as Firestore).collection(`filament-purchases/${purchaseId}/Items`));
//   if (!file) {
//     // return NextResponse.json({ success: false })
//     throw new Error("No file found in form data")
//   }
//   let filename = file.name;


export async function ImportAmazonListings() {
  let result = await importListingsToFirestore(marketplaceIds.US)
  console.log("ImportAmazonListings result", result)

}

export async function addShopifyVariantIdsToFirestore() {
  console.log("addShopifyVariantIdsToFirestore starting")

  //let shopProducts = await shopifyNode.getProductsInCollection(filamentCollection);
  let shopProducts = await getProductsByFilamentType();
  console.log("shopProducts", shopProducts)

  const products = await firebase.getProducts<Product>();
  console.log("Products: ", products.map(i => i.id))

  for (let shopProduct of shopProducts) {
    for (let variant of shopProduct.variants) {
      let isCase = variant.sku?.toLowerCase().endsWith('-12');
      let shopSku = variant.sku?.toLowerCase()
      let shopMainSku = isCase ? shopSku.replace('-12', '') : shopSku;
      let product = products.find((p: Product) => {
        return (p.id === shopMainSku || p.skuMaps?.includes(shopMainSku))
      });
//      if (product?.shopify?.sku && product?.shopify?.caseSku) { continue }
      
      if (product && product.id) {
        let skuField = isCase ? 'shopify.variantCase' : 'shopify.variant';
        let data = { [skuField] : {
          sku: shopSku,
          id: variant.id,
          price: variant.price,
        },
       };
       if (!isCase) {
        data['shopify.title'] = shopProduct.title;
        data['shopify.price'] = variant.price;
       }

        // let data = 
        // { shopify: {
        //   [skuField]: {
        //     sku: shopSku,
        //     id: variant.id,
        //   } 
        // } };
        console.log(product.id, data)
        firebase.updateProduct(product.id as string, data);
      }
      else {
        console.log(`No product found for ${shopSku}`)
        continue;
      }
    }
  }
}
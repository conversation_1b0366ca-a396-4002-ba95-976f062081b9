"use client";
import { useRef, useState, useEffect, useMemo } from 'react';
import { toast } from 'sonner';
import LoadingButton from '~/components/loadingButton';
import CcGrid from "~/components/cc-grid";
import { ColDef, CellValueChangedEvent } from "ag-grid-community";
import firebaseClient from "~/data/google/firebase-client";
import QuickPrompt, { QuickPromptRef } from "~/components/quickPrompt";
import SelectEditor from '~/components/selectEditor';
import { getXeroAccounts, getXeroRules, type XeroRule } from './actions';

interface AmazonPurchase {
  id?: string;
  paymentId: string;
  orderNumber: string;
  orderDate: string;
  title: string;
  class: string;
  amount: number;
  status: string;
  accountCode?: string;
  xeroInvoiceId?: string;
  xeroStatus?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface AmazonReconciliation {
  id?: string;
  transactionDate: string;
  paymentRefId: string;
  transactionType: string;
  currency: string;
  paymentAmount: number;
  accountGroup: string;
  paymentInstrument: string;
  paymentIdentifier: string;
  chargeIdentifier: string;
  accountUser: string;
  orderDate: string;
  orderId: string;
  title?: string;
  segment?: string;
  family?: string;
  class?: string;
  brand?: string;
  xeroInvoiceId?: string;
  xeroStatus?: string;
  xeroCategoryCode?: string; // User-selected Xero category code
  calculatedCategoryCode?: string; // Category code calculated from rules
  xeroCategoryName?: string; // Display name for the Xero category
  createdAt: Date;
  updatedAt: Date;
}

export default function AccountingUtils() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const reconFileInputRef = useRef<HTMLInputElement>(null);
  const datePromptRef = useRef<any>(null);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [rowData, setRowData] = useState<AmazonReconciliation[]>([]);
  const [loading, setLoading] = useState(false);
  const [xeroRules, setXeroRules] = useState<XeroRule[]>([]);
  const [accountOptions, setAccountOptions] = useState<{value: string, label: string}[]>([]);
  const gridRef = useRef<any>(null);

  // Column definitions for the Amazon Reconciliation grid
  const [colDefs, setColDefs] = useState<ColDef[]>([
    { field: "id", hide: true },
    { field: "paymentRefId", headerName: "Payment Ref ID", sortable: true, filter: true },
    { field: "orderId", headerName: "Order ID", sortable: true, filter: true },
    {
      field: "transactionDate",
      headerName: "Transaction Date",
      sortable: true,
      filter: true,
      valueFormatter: (params) => {
        return params.value ? new Date(params.value).toLocaleDateString() : "";
      }
    },
    { field: "xeroStatus", headerName: "Xero Status", sortable: true, filter: true },
    { field: "xeroInvoiceId", headerName: "Xero Invoice ID", sortable: true, filter: true },
    {
      field: "xeroCategoryCode",
      headerName: "Xero Category",
      sortable: true,
      filter: true,
      editable: true,
      cellEditor: SelectEditor,
      cellEditorParams: {
        options: accountOptions
      },
      valueFormatter: (params) => {
        if (!params.value) {
          return params.data?.calculatedCategoryCode ?
            `(${params.data.calculatedCategoryCode}) Auto` :
            "";
        }
        return params.data?.xeroCategoryName || params.value;
      }
    },
    {
      field: "orderDate",
      headerName: "Order Date",
      sortable: true,
      filter: true,
      valueFormatter: (params) => {
        return params.value ? new Date(params.value).toLocaleDateString() : "";
      }
    },
    { field: "title", headerName: "Title", sortable: true, filter: true },
    { field: "transactionType", headerName: "Type", sortable: true, filter: true },
    {
      field: "paymentAmount",
      headerName: "Amount",
      sortable: true,
      filter: true,
      valueFormatter: (params) => {
        return params.value ? params.value.toLocaleString('en-US', {
          style: 'currency',
          currency: 'USD',
        }) : "";
      }
    },

    { field: "segment", headerName: "Segment", sortable: true, filter: true },
    { field: "family", headerName: "Family", sortable: true, filter: true },
    { field: "class", headerName: "Class", sortable: true, filter: true },
    { field: "brand", headerName: "Brand", sortable: true, filter: true },
    { field: "accountUser", headerName: "Account User", sortable: true, filter: true },
  ]);

  const defaultColDef = useMemo<ColDef>(() => ({
    editable: false,
    sortable: true,
    filter: true,
    resizable: true,
  }), []);

  // Load data on component mount
  useEffect(() => {
    // Load Xero accounts first
    const loadAccounts = async () => {
      try {
        const accounts = await getXeroAccounts();
        setAccountOptions(accounts);
        
        // Update the column definitions with the new account options
        setColDefs(prev => prev.map(col => {
          if (col.field === 'xeroCategoryCode') {
            return {
              ...col,
              cellEditorParams: {
                options: accounts
              }
            };
          }
          return col;
        }));
        
        toast.success('Xero accounts loaded successfully');
      } catch (error) {
        console.error('Error loading Xero accounts:', error);
        toast.error('Failed to load Xero accounts: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    };
    
    // Load Xero rules
    const loadRules = async () => {
      try {
        const rules = await getXeroRules();
        setXeroRules(rules);
        toast.success('Xero rules loaded successfully');
      } catch (error) {
        console.error('Error loading Xero rules:', error);
        toast.error('Failed to load Xero rules: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    };
    
    loadAccounts();
    loadRules();
    fetchAmazonReconciliation();
  }, []);

  // Apply Xero rules to calculate category codes
  const applyXeroRules = (items: AmazonReconciliation[]): AmazonReconciliation[] => {
    if (!xeroRules.length) return items;
    
    return items.map(item => {
      // Skip if user already set a custom category
      if (item.xeroCategoryCode) return item;
      
      // Find matching rule for this class
      if (item.class) {
        const matchingRule = xeroRules.find(rule =>
          rule.field === 'class' && rule.value === item.class
        );
        
        if (matchingRule) {
          const accountName = accountOptions.find(opt => opt.value === matchingRule.account)?.label || '';
          
          return {
            ...item,
            calculatedCategoryCode: matchingRule.account,
            xeroCategoryName: accountName
          };
        }
      }
      
      return item;
    });
  };

  // Fetch Amazon reconciliation data from Firestore
  const fetchAmazonReconciliation = async () => {
    setLoading(true);
    try {
      const data = await firebaseClient.queryCollection<AmazonReconciliation>('amazon.com-reconciliation', [['xeroStatus', '!=', 'PAID']])
      
      // Apply Xero rules to calculate category codes
      const processedData = applyXeroRules(data);
      setRowData(processedData);
    } catch (error) {
      toast.error('Failed to load Amazon reconciliation data: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };
  
  // Handle changes to Xero category selection
  const handleCellValueChanged = async (params: CellValueChangedEvent) => {
    if (params.colDef.field === 'xeroCategoryCode' && params.data) {
      const { id } = params.data;
      const newCode = params.newValue;
      
      if (!id) return;
      
      try {
        // Find the account name for display
        const accountName = accountOptions.find(opt => opt.value === newCode)?.label || '';
        
        // Update in Firestore
        await firebaseClient.updateData('amazon.com-reconciliation', id, {
          xeroCategoryCode: newCode,
          xeroCategoryName: accountName,
          updatedAt: new Date()
        });
        
        // Update local state
        setRowData(prev => prev.map(item =>
          item.id === id
            ? { ...item, xeroCategoryCode: newCode, xeroCategoryName: accountName }
            : item
        ));
        
        toast.success('Xero category updated successfully');
      } catch (error) {
        console.error('Error updating Xero category:', error);
        toast.error('Failed to update Xero category: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }
    }
  };

  // Function to upload Amazon purchases CSV
  function uploadAmazonPurchases() {
    const url = '/api/xero/amazon-purchases';
    const file = fileInputRef.current?.files?.[0];
    if (!file) {
      toast.error('No file selected');
      return;
    }
    
    setUploadResult(null);
    const formData = new FormData();
    formData.append('file', file);
    formData.append('reportType', 'purchases'); // Specify report type
    
    toast.promise(
      fetch(url, {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        console.log(data);
        setUploadResult(data);
        return data;
      }),
      {
        loading: 'Uploading Amazon purchases...',
        success: (data) => 'Amazon purchases uploaded to Firestore successfully',
        error: 'Failed to upload Amazon purchases'
      }
    );
  }

  // Function to upload Amazon reconciliation CSV
  function uploadAmazonReconciliation() {
    const url = '/api/xero/amazon-purchases';
    const file = reconFileInputRef.current?.files?.[0];
    if (!file) {
      toast.error('No file selected');
      return;
    }
    
    setUploadResult(null);
    const formData = new FormData();
    formData.append('file', file);
    formData.append('reportType', 'reconciliation'); // Specify report type
    
    toast.promise(
      fetch(url, {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        console.log(data);
        setUploadResult(data);
        fetchAmazonReconciliation(); // Refresh the grid after upload
        return data;
      }),
      {
        loading: 'Uploading Amazon reconciliation data...',
        success: (data) => 'Amazon reconciliation data uploaded to Firestore successfully',
        error: 'Failed to upload Amazon reconciliation data'
      }
    );
  }

  // Function to push purchases to Xero
  const pushToXero = () => {
    datePromptRef.current?.open(null, async (dateString: string) => {
      if (!dateString) {
        toast.error('Date is required');
        return;
      }

      setLoading(true);
      try {
        const response = await fetch('/api/xero/amazon-purchases', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ startDate: dateString }),
        });

        const result = await response.json();
        
        if (result.success) {
          toast.success(`Processed ${result.processedCount} purchases to Xero successfully`);
          if (result.errorCount > 0) {
            toast.warning(`${result.errorCount} purchases had errors during processing`);
          }
          fetchAmazonReconciliation(); // Refresh the grid to show updated statuses
        } else {
          toast.error('Failed to push purchases to Xero');
        }
      } catch (error) {
        toast.error('Error pushing to Xero: ' + (error instanceof Error ? error.message : 'Unknown error'));
      } finally {
        setLoading(false);
      }
    });
  };

  // Function to update Xero statuses
  const updateXeroStatuses = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/xero/amazon-purchases', {
        method: 'PATCH',
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success(`Updated statuses for ${result.updatedCount} purchases successfully`);
        fetchAmazonReconciliation(); // Refresh the grid to show updated statuses
      } else {
        toast.error('Failed to update Xero statuses');
      }
    } catch (error) {
      toast.error('Error updating Xero statuses: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <main className="flex flex-1 min-h-screen flex-col p-6 h-[100%]">
      <div className="flex flex-col flex-1 gap-8 w-full">
        <h1 className="text-2xl font-bold">Accounting Utilities</h1>
        
        {/* Amazon Purchases Upload Section */}
        <div className="border rounded-md p-4 bg-white shadow-sm">
          <h2 className="text-lg font-semibold mb-4">Upload Amazon Files</h2>
          <div className="flex flex-col gap-6">
            {/* Purchase Orders Upload */}
            <div className="flex flex-col gap-2">
              <p className="text-sm text-gray-600">
                Upload a CSV file containing Amazon purchase data to save to Firestore.
              </p>
              <div className="flex gap-4 items-center">
                <input 
                  ref={fileInputRef}
                  className="w-[300px] text-sm" 
                  type="file" 
                  accept=".csv,.tsv,.txt"
                />
                <LoadingButton type="submit" onClick={uploadAmazonPurchases}>
                  Upload Purchase Orders
                </LoadingButton>
              </div>
            </div>

            {/* Reconciliation Report Upload */}
            <div className="flex flex-col gap-2">
              <p className="text-sm text-gray-600">
                Upload a CSV file containing Amazon reconciliation data to save to Firestore.
              </p>
              <div className="flex gap-4 items-center">
                <input 
                  ref={reconFileInputRef}
                  className="w-[300px] text-sm" 
                  type="file" 
                  accept=".csv,.tsv,.txt"
                />
                <LoadingButton type="submit" onClick={uploadAmazonReconciliation}>
                  Upload Reconciliation Report
                </LoadingButton>
              </div>
            </div>
            
            {uploadResult && (
              <div className="mt-4 p-3 border rounded-md bg-gray-50">
                <h3 className="font-medium">Upload Result:</h3>
                <pre className="text-xs mt-2 overflow-auto">
                  {JSON.stringify(uploadResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* Amazon Reconciliation Grid Section */}
        <div className="flex flex-col flex-1 border rounded-md p-4 bg-white shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Amazon Reconciliation Data</h2>
            <div className="flex gap-4">
              <LoadingButton 
                onClick={pushToXero} 
                loading={loading} 
                color="primary"
              >
                Push to Xero
              </LoadingButton>
              <LoadingButton 
                onClick={updateXeroStatuses} 
                loading={loading} 
                color="secondary"
              >
                Update Xero Statuses
              </LoadingButton>
              <LoadingButton 
                onClick={fetchAmazonReconciliation} 
                loading={loading}
                color="default"
              >
                Refresh
              </LoadingButton>
            </div>
          </div>

          {/* The Grid */}
          <div 
        className="flex flex-1 w-full mt-10"
        style={{ height: "calc(100% - 100px)" }}
        >
            <CcGrid
              ref={gridRef}
              rowData={rowData}
              colDefs={colDefs}
              setColDefs={setColDefs}
              autoAddMissingColumns={true}
              additionalOptions={{
                defaultColDef,
                enableCellTextSelection: true,
                domLayout: "normal",
                onCellValueChanged: handleCellValueChanged,
              }}
            />
          </div>
          <div className="mt-2 text-sm">
            Total Row Count: {rowData.length}
          </div>
        </div>
      </div>

      {/* Date Prompt for Xero Push */}
      <QuickPrompt
        ref={datePromptRef}
        title="Select Start Date"
        message="Enter the start date from which to push purchases to Xero (YYYY-MM-DD)"
      />
    </main>
  );
}
"use server"

import { shopifyNode } from 'cookiecad-shared'
import firebase from "~/data/google/firebase-server";

interface Product {
  id: string;
  skuMaps?: string[];
  upc?: string;
  [key: string]: any;
}

const filamentCollection = 224410534048;

export async function addUPC() {
  const listings = await firebase.getAmazonListings();
  console.log("Listings: ", listings);
  for (let listing of listings) {
    let upc = listing.catalogItem?.identifiers[0].identifiers[1].identifier;
    if (upc && listing.cookiecadSku && listing.cookiecadSku != "inactive") {
      firebase.updateProduct(listing.cookiecadSku, { upc: upc });
    }
  }
}

export async function addSkuMaps() {
  console.log("addSkuMaps starting")

  let shopProducts = await shopifyNode.getProductsInCollection(filamentCollection);

  const products = await firebase.getProducts<Product>();
  console.log("Products: ", products.map(i => i.id))

  for (let shopProduct of shopProducts) {
    for (let variant of shopProduct.variants) {
      let shopSku = variant.sku?.toLowerCase().trim();
      if (shopSku) { 
        let shopSkuMapped = skuMap(shopSku) || shopSku;
        let product = products.find((p: Product) => {
          return (p.id == shopSkuMapped)
        });

        console.log(shopSku, "product", product?.id)

        if (product && product.id) {
          let data = { skuMaps: [ shopSku ] };
          firebase.updateProduct(product.id as string, data);
        }
        else {
          console.log(`No product found for ${shopSku} - ${shopSkuMapped}`)
          continue;
        }
      }
    }
  }
}

export async function updateShopBarcode() {
  let shopProducts = await shopifyNode.getProductsInCollection(filamentCollection);
  let products = await firebase.getProducts<Product>();

  for (const shopProduct of shopProducts) {
    for (const variant of shopProduct.variants) {
      let shopSku = variant.sku?.toLowerCase().trim();
      for (const product of products) {
        if (product.skuMaps?.includes(shopSku)) {
          let barcode = product.upc;
          if (barcode) {
            if (variant.barcode) {
              if (variant.barcode != barcode) {
                console.error(`Barcode mismatch: ${variant.barcode} != ${barcode} for ${shopSku}`)
              }
            }
            else {
              let data = { barcode: barcode };
              await shopifyNode.shopify.productVariant.update(variant.id, data);
            }
          }
        }
      }
    }
  }
}

function skuMap(sku: string) {
  // Process SKU: lowercase and replace spaces and '-fba'
  sku = sku.toLowerCase().replace(/ /g, '-').replace('-fba', '');

      if (sku == '9n-8vo7-joai') {  return  'pla-mint-green' }
      if (sku == 'mr-qx4k-v41o') {  return  'pla-coral' }
      if (sku == 'pale-blue-dot') {  return  'pla-pale-blue-dot' }
      if (sku == 'candy-apple-red') {  return  'pla-candy-apple-red' }
      if (sku == 'bubblegum-pink') {  return  'pla-bubblegum' }
      if (sku == 'elixer-witches-brew') {  return  'pla-elixir-witches-blue' }
      if (sku == 'unicorn-dual-silk') {  return  'dual-silk-unicorn' }
      if (sku == 'granite-dual-silk') {  return  'dual-silk-granite' }
      if (sku == 'sunrise-dual-silk') {  return  'dual-silk-sunrise' }
      if (sku == 'mermaid-silk') {  return  'silk-mermaid' }
      if (sku == 'unicorn-silk') {  return  'silk-unicorn' }
      if (sku == 'cookiecad-black') {  return  'pla-black' }
      if (sku == 'cookiecad-white') {  return  'pla-white' }
      if (sku == 'pixie-dust') {  return  'pla-elixir-pixie-dust' }
      if (sku == 'elixir-gold') {  return  'pla-elixir-solar-flare' }
      if (sku == 'glitter-black') {  return  'pla-star-dust' }
      if (sku == 'black') {  return  'pla-black' }
      if (sku == 'white') {  return  'pla-white' }
      if (sku == 'sunrise') {  return  'pla-sunrise' }
      if (sku == 'mermaid') {  return  'pla-mermaid' }
      if (sku == 'dark-magic') {  return  'pla-dark-magic' }
      if (sku == 'unicorn') {  return  'pla-unicorn' }
      if (sku == 'star-dust') {  return  'pla-star-dust' }
      if (sku == 'pink-ombre') {  return  'pla-pink-ombre' }
      if (sku == 'red') {  return  'pla-candy-apple-red' }
      if (sku == 'blue-ombre') {  return  'pla-blue-ombre' }
      if (sku == 'purple-ombre') {  return  'pla-purple-ombre' }
      if (sku == 'bubblegum') {  return  'pla-bubblegum' }
      if (sku == 'light-blue') {  return  'pla-pale-blue-dot' }
      if (sku == 'pale-pink') {  return  'pla-pale-pink' }
      if (sku == 'sunset') {  return  'pla-sunset' }
      if (sku == 'hot-pink') {  return  'pla-hot-pink' }
      if (sku == 'pink-ombre-silk') {  return  'silk-pink-ombre' }
      if (sku == 'lavender') {  return  'pla-lavender' }
      if (sku == 'witchcraft') {  return  'pla-witchcraft' }
      if (sku == 'sunrise') {  return  'pla-sunrise' }
      if (sku == 'coral') {  return  'pla-coral' }
      if (sku == 'green-apple') {  return  'pla-green-apple' }
      if (sku == 'grey-ombre') {  return  'pla-grey-ombre' }
      if (sku == 'mint-green') {  return  'pla-mint-green' }
      if (sku == 'orange-sorbet') {  return  'pla-orange-sorbet' }
      if (sku == 'pale-yellow') {  return  'pla-pale-yellow' }
      if (sku == 'periwinkle') {  return  'pla-periwinkle' }
      if (sku == 'dual-mermaid-silk') {  return  'dual-silk-mermaid' }
      if (sku == 'silk-dual-mermaid') {  return  'dual-silk-mermaid' }
      if (sku == 'black-star-stuff') {  return  'pla-black-star-stuff' }
      if (sku == 'black-white-mixer') {  return  'pla-mixer-granite' }
      if (sku == 'blue-star-stuff') {  return  'pla-blue-star-stuff' }
      if (sku == 'elixer-gold') {  return  'pla-elixir-solar-flare' }
      if (sku == 'elixir-witches-blue') {  return  'pla-elixir-witches-blue' }
      if (sku == 'elixir-pale-pink') {  return  'pla-elixir-pale-pink' }
      if (sku == 'elixir-pale-blue') {  return  'pla-elixir-pale-blue' }
      if (sku == 'elixir-mint-green') {  return  'pla-elixir-mint-green' }
      if (sku == 'elixir-lavender') {  return  'pla-elixir-lavender' }
      if (sku == 'elixir-green-apple') {  return  'pla-elixir-green-apple' }
      if (sku == 'elixir-sunrise') {  return  'pla-elixir-sunrise' }
      if (sku == 'elixir-sunset') {  return  'pla-elixir-sunset' }
      if (sku == 'elixir-unicorn') {  return  'pla-elixir-unicorn' }
      if (sku == 'elixir-pla-unicorn') {  return  'pla-elixir-unicorn' }
      if (sku == 'elixir-pixie-dust') {  return  'pla-elixir-pixie-dust' }
      if (sku == 'pla-pixie-dust') {  return  'pla-elixir-pixie-dust' }
      if (sku == 'elixir-pla-green-apple') {  return  'pla-elixir-green-apple' }
      if (sku == 'lavender-petg') {  return  'petg-lavender' }
      if (sku == 'mint-green-petg') {  return  'petg-mint-green' }
      if (sku == 'mermaid-petg') {  return  'petg-mermaid' }
      if (sku == 'unicorn-petg') {  return  'petg-unicorn' }
      if (sku == 'pla-gray-ombre') {  return  'pla-grey-ombre' }
      if (sku == 'gray-ombre') {  return  'pla-grey-ombre' }
      if (sku == 'elixir-pla-sunrise') {  return  'pla-elixir-sunrise' }
      if (sku == 'unicorn-mixer') {  return  'pla-mixer-unicorn' }
      if (sku == 'mixer-unicorn') {  return  'pla-mixer-unicorn' }
      if (sku == 'dual-unicorn-mixer') {  return  'pla-mixer-unicorn' }
      if (sku == 'dual-granite-mixer') { return 'pla-mixer-granite' }
      if (sku == 'mixer-sunrise') {  return  'pla-mixer-sunrise' }
      if (sku == 'mixer-granite') {  return  'pla-mixer-granite' }
      if (sku == 'sunrise-mixer') {  return  'pla-mixer-sunrise' }
      if (sku == 'dual-sunrise-mixer') {  return  'pla-mixer-sunrise' }
      if (sku == 'fast-pla-unicorn') {  return  'pla-fast-unicorn' }
      if (sku.includes('purple-petg')) {  return  'petg-lavender' }
      if (sku.startsWith('vanilla-chip')) {  return  'pla-vanilla-chip' }
      if (sku.startsWith('pink-chip')) {  return  'pla-pink-chip' }
      if (sku.startsWith('lavender-chip')) {  return  'pla-lavender-chip' }
      if (sku.startsWith('blue-chip')) {  return  'pla-blue-chip' }
      if (sku.startsWith('mint-chip')) {  return  'pla-mint-chip' }
      if (sku.startsWith('purple-ombr')) {  return  'pla-purple-ombre' }
      if (sku.startsWith('elixer')) {  return sku.replace('elixer', 'pla-elixir') }
      if (sku.startsWith('mermaid-fast')) {  return  'pla-fast-mermaid' }
      if (sku.startsWith('unicorn-fast')) {  return  'pla-fast-unicorn' }
      if (sku.startsWith('silk-dual-unicorn')) {  return  'dual-silk-unicorn' }
      if (sku.startsWith('silk-dual-granite')) {  return  'dual-silk-granite' }
      if (sku.startsWith('silk-dual-sunrise')) {  return  'dual-silk-sunrise' }
      if (sku.startsWith('cookiecad-granite')) {  return  'dual-silk-granite' }
      if (sku.startsWith('silk-pink-ombr')) {  return  'silk-pink-ombre' }
      if (sku.startsWith('silk-mermaid-pla')) {  return  'silk-mermaid' }
      if (sku.startsWith('silk-unicorn-pla')){  return 'silk-unicorn' }
      if (sku.startsWith('coral-pla')) {  return  'pla-coral' }
      if (sku.includes('funfetti')) {  return  'pla-funfetti' }
      if (sku.includes('dark-magic-pla')) {  return  'pla-dark-magic' }
      if (sku.includes('dark-magic-petg')) {  return  'petg-dark-magic' }
      if (sku.includes('petg-dark-magic')) {  return  'petg-dark-magic' }
      if (sku.includes('witchcraft-pla')) {  return  'pla-witchcraft' }
      if (sku.includes('petg-witchcraft')) {  return  'petg-witchcraft' }
      if (sku.startsWith('mint-green-petg')) {  return  'petg-mint-green' }
      if (sku.startsWith('pale-blue-elixir')) {  return  'pla-elixir-pale-blue' }
      if (sku.startsWith('lavender-elixir')) {  return  'pla-elixir-lavender' }
      if (sku.startsWith('witches-blue')) {  return  'pla-elixir-witches-blue' }
      if (sku.includes('solar-flare')) {  return  'pla-elixir-solar-flare' }
      if (sku.startsWith('pale-pink-elixir')) {  return  'pla-elixir-pale-pink' }
      if (sku.startsWith('lavender-elixir')) {  return  'pla-elixir-lavender' }
      if (sku.startsWith('pale-blue-elixir')) {  return  'pla-elixir-pale-blue' }
      if (sku.startsWith('mint-green-elixir')) {  return  'pla-elixir-mint-green' }
}

import { AbstractFirestoreService } from "../google/firestore/AbstractFirestoreService";

import type {
  PurchaseOrder,
  PurchaseOrderItem,
  ReadPurchaseOrder,
  CreatePurchaseOrderData,
  CreatePurchaseOrderItemData,
  UpdatePurchaseOrderData,
  UpdatePurchaseOrderItemData,
  OutstandingItem,
  InvoiceSummary,
  ItemInvoice, // Added for itemInvoiceData
} from './types';

// Helper function for safe error message extraction
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  if (typeof error === 'string') return error;
  return String(error);
}

export class PurchaseOrderRepository {
  private readonly collection = 'purchase-orders';
  private readonly itemsCollection = 'purchase-order-items';

  constructor(private firestoreLib: AbstractFirestoreService) {}

  /**
   * Convert nested objects to dot notation
   * e.g. { invoices: { 'INV-1': { status: 'PAID' } } }
   * becomes { 'invoices.INV-1.status': 'PAID' }
   */
  private toDotNotation(obj: Record<string, any>, prefix = ''): Record<string, any> {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      // Check if this is a Firebase deleteField() function
      if (value && typeof value === 'object' && value._methodName === 'deleteField') {
        // Preserve deleteField() functions as-is
        acc[newKey] = value;
      } else if (value && typeof value === 'object' && !(value instanceof Date)) {
        Object.assign(acc, this.toDotNotation(value, newKey));
      } else {
        acc[newKey] = value;
      }
      
      return acc;
    }, {} as Record<string, any>);
  }

  /**
   * Update invoice fields with type checking
   */
  async updateInvoice(
    poNumber: string,
    invoiceId: string,
    data: Partial<InvoiceSummary>
  ): Promise<void> {
    // Runtime type validation
    if (data.paidDate && !(data.paidDate instanceof Date)) {
      throw new Error('paidDate must be a Date object');
    }

    const dotNotation = this.toDotNotation({ invoices: { [invoiceId]: data } });
    await this.update(poNumber, dotNotation);
  }

  // Get a single purchase order by ID
  async get(poId: string): Promise<ReadPurchaseOrder | null> {
    try {
      const doc = await this.firestoreLib.get<ReadPurchaseOrder>(this.collection, poId);
      return doc;
    } catch (error) {
      throw new Error(`Failed to get purchase order: ${poId}: ${getErrorMessage(error)}`);
    }
  }

  // Get all purchase orders
  async getAll(): Promise<ReadPurchaseOrder[]> {
    try {
      const docs = await this.firestoreLib.getAll<ReadPurchaseOrder>(this.collection);
      return docs;
    } catch (error) {
      throw new Error(`Failed to get all purchase orders: ${getErrorMessage(error)}`);
    }
  }

  // Get non-archived purchase orders
  async getNonArchived(): Promise<ReadPurchaseOrder[]> {
    try {
      const docs = await this.firestoreLib.queryCollection<ReadPurchaseOrder>(this.collection, [
        ["status", "!=", "ARCHIVED"]
      ]);
      return docs;
    } catch (error) {
      throw new Error(`Failed to get non-archived purchase orders: ${getErrorMessage(error)}`);
    }
  }

  // Create a new purchase order
  async create(data: CreatePurchaseOrderData): Promise<void> {
    try {
      const { "po-number": poNumber, ...rest } = data;
      await this.firestoreLib.setData(this.collection, poNumber, {
        ...rest,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (error) {
      throw new Error(`Failed to create purchase order: ${getErrorMessage(error)}`);
    }
  }

  // Update a purchase order
  async update(
    poNumber: string, 
    data: UpdatePurchaseOrderData
  ): Promise<void> {
    try {
      const dotNotation = this.toDotNotation(data);
      await this.firestoreLib.updateData(this.collection, poNumber, {
        ...dotNotation,
        updatedAt: new Date()
      });
    } catch (error) {
      throw new Error(`Failed to update purchase order: ${poNumber}: ${getErrorMessage(error)}`);
    }
  }

  // Add an item to a purchase order
  async addItem(
    poNumber: string, 
    item: CreatePurchaseOrderItemData
  ): Promise<string> {
    try {
      console.log("Repository addItem: Creating item in collection:", this.collection, "subcollection:", this.itemsCollection, "for PO:", poNumber);
      const data = {
        ...item,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      console.log("Repository addItem: Data to be saved:", data);
      
      const result = await this.firestoreLib.setSubData(
        this.collection,
        this.itemsCollection,
        poNumber,
        data
      );
      
      console.log("Repository addItem: Received result:", result);
      if (!result) {
        throw new Error('Failed to get ID from created document');
      }
      
      return result;
    } catch (error) {
      throw new Error(`Failed to add item to purchase order: ${poNumber}: ${getErrorMessage(error)}`);
    }
  }

  // Get items for a purchase order
  async getItems(poNumber: string): Promise<PurchaseOrderItem[]> {
    try {
      const docs = await this.firestoreLib.getAll<PurchaseOrderItem>(this.collection, poNumber, this.itemsCollection);
      return docs;
    } catch (error) {
      throw new Error(`Failed to get items for purchase order: ${poNumber}: ${getErrorMessage(error)}`);
    }
  }

  // Update a purchase order item
  async updateItem(
    poNumber: string, 
    itemId: string, 
    data: UpdatePurchaseOrderItemData
  ): Promise<void> {
    try {
      await this.firestoreLib.updateSubcollection(
        this.collection,
        poNumber,
        this.itemsCollection,
        itemId,
        {
          ...data,
          updatedAt: new Date()
        }        
      );
    } catch (error) {
      throw new Error(`Failed to update item ${itemId} in purchase order: ${poNumber}: ${getErrorMessage(error)}`);
    }
  }

  // Delete a purchase order item
  async deleteItem(
    poNumber: string,
    itemId: string
  ): Promise<void> {
    try {
      await this.firestoreLib.deleteDoc(`${this.collection}/${poNumber}/${this.itemsCollection}`, itemId); 
    } catch (error) {
      throw new Error(`Failed to delete item ${itemId} from purchase order: ${poNumber}: ${getErrorMessage(error)}`);
    }
  }

  // Delete a purchase order
  async delete(poNumber: string): Promise<void> {
    try {
      await this.firestoreLib.deleteDoc(this.collection, poNumber);
    } catch (error) {
      throw new Error(`Failed to delete purchase order: ${poNumber}: ${getErrorMessage(error)}`);
    }
  }

  // Get purchase orders with invoices
  async getWithInvoices(): Promise<PurchaseOrder[]> {
    try {
      const docs = await this.firestoreLib.queryCollection<PurchaseOrder>(this.collection, [
        ["invoices", "!=", {}]
      ]);
      return docs;
    } catch (error) {
      throw new Error(`Failed to get purchase orders with invoices: ${getErrorMessage(error)}`);
    }
  }

  // Calculate outstanding items for specific POs
  async calculateOutstandingItems(poNumbers: string[]): Promise<OutstandingItem[]> {
    try {
      const outstandingItems = new Map<string, OutstandingItem>();

      for (const poNumber of poNumbers) {
        const items = await this.getItems(poNumber);
        
        for (const item of items) {
          // Calculate outstanding quantity
          const invoicedQuantity = Object.values(item.invoices || {})
            .reduce((acc, inv) => acc + (inv.quantity || 0), 0);
          const outstandingQuantity = item.quantity - invoicedQuantity;

          if (outstandingQuantity > 0) {
            const existingItem = outstandingItems.get(item.sku);
            if (existingItem) {
              existingItem.quantities[poNumber] = {
                quantity: outstandingQuantity,
                poNumber
              };
              existingItem.total += outstandingQuantity;
            } else {
              outstandingItems.set(item.sku, {
                sku: item.sku,
                quantities: {
                  [poNumber]: {
                    quantity: outstandingQuantity,
                    poNumber
                  }
                },
                total: outstandingQuantity,
                metadata: {
                  lastUpdated: new Date()
                }
              });
            }
          }
        }
      }

      return Array.from(outstandingItems.values());
    } catch (error) {
      throw new Error(`Failed to calculate outstanding items: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Creates an invoice in the PurchaseOrder and updates specified PurchaseOrderItems.
   * Note: This operation is not currently atomic across documents.
   * Consider implementing Firestore transactions or batched writes if firebase-lib supports it.
   */
  async createInvoiceAndUpdateItems(
    poId: string,
    invoiceId: string,
    invoiceData: InvoiceSummary, // For the PO
    itemsToUpdate: Array<{ itemId: string, itemInvoiceData: ItemInvoice }> // For POItems
  ): Promise<void> {
    try {
      // 1. Add/Update the invoiceId in the PurchaseOrder.invoices map
      const poUpdateData = {
        invoices: {
          [invoiceId]: invoiceData,
        },
      };
      await this.update(poId, poUpdateData);

      // 2. For each item in itemsToUpdate, add/update the invoice entry in PurchaseOrderItem.invoices
      for (const { itemId, itemInvoiceData } of itemsToUpdate) {
        // It's crucial to merge with existing invoices on the item, not overwrite.
        // Fetch the item first to get existing invoices, or update atomically if possible.
        // For simplicity here, we'll update the specific invoice key.
        // A more robust solution would fetch the item, merge invoices, then update.
        const itemUpdateData = {
          invoices: {
            [invoiceId]: itemInvoiceData,
          },
        };
        await this.updateItem(poId, itemId, itemUpdateData);
      }
    } catch (error) {
      throw new Error(`Failed to create invoice ${invoiceId} for PO ${poId} and update items: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Deletes an invoice from the PurchaseOrder and all related PurchaseOrderItems.
   * Note: This operation is not currently atomic across documents.
   * Consider implementing Firestore transactions or batched writes if firebase-lib supports it.
   */
  async deleteInvoiceAndUpdateItems(poId: string, invoiceId: string): Promise<void> {
    try {
      // 1. Remove the invoiceId from the PurchaseOrder.invoices map
      // Using dot notation for field deletion
      const poUpdateData = {
        [`invoices.${invoiceId}`]: this.firestoreLib.deleteField(),
      };
      await this.update(poId, poUpdateData);

      // 2. Iterate through all PurchaseOrderItem's for the given poId and remove the invoiceId
      const items = await this.getItems(poId);
      for (const item of items) {
        if (item.invoices && item.invoices[invoiceId]) {
          const itemUpdateData = {
            [`invoices.${invoiceId}`]: this.firestoreLib.deleteField(),
          };
          // Ensure item.id exists, as getItems should return items with IDs
          if (item.id) {
            await this.updateItem(poId, item.id, itemUpdateData);
          } else {
            console.warn(`Item in PO ${poId} missing an ID, cannot update for invoice deletion:`, item);
          }
        }
      }
    } catch (error) {
      throw new Error(`Failed to delete invoice ${invoiceId} for PO ${poId} and update items: ${getErrorMessage(error)}`);
    }
  }

  /**
   * Renames an invoice in the PurchaseOrder and all related PurchaseOrderItems.
   * Note: This operation is not currently atomic across documents.
   * Consider implementing Firestore transactions or batched writes if firebase-lib supports it.
   */
  async renameInvoiceAndUpdateItems(
    poId: string,
    oldInvoiceId: string,
    newInvoiceId: string,
    newInvoiceDataForPO: InvoiceSummary // This is the new summary for the PO
  ): Promise<void> {
    try {
      if (oldInvoiceId === newInvoiceId) {
        // If IDs are the same, just update the content in PO if necessary
        await this.update(poId, { [`invoices.${newInvoiceId}`]: newInvoiceDataForPO });
        return;
      }

      // 1. Rename the key in PurchaseOrder.invoices and update its content
      const poUpdateData = {
        [`invoices.${oldInvoiceId}`]: this.firestoreLib.deleteField(),
        [`invoices.${newInvoiceId}`]: newInvoiceDataForPO,
      };
      await this.update(poId, poUpdateData);

      // 2. Iterate through all PurchaseOrderItem's for the given poId
      const items = await this.getItems(poId);
      for (const item of items) {
        if (item.invoices && item.invoices[oldInvoiceId]) {
          const oldItemInvoiceData = item.invoices[oldInvoiceId];
          const newItemInvoiceData: ItemInvoice = {
            ...oldItemInvoiceData,
            id: newInvoiceId, // Update the id field within the invoice object
          };

          const itemUpdateData = {
            [`invoices.${oldInvoiceId}`]: this.firestoreLib.deleteField(),
            [`invoices.${newInvoiceId}`]: newItemInvoiceData,
          };

          if (item.id) {
            await this.updateItem(poId, item.id, itemUpdateData);
          } else {
            console.warn(`Item in PO ${poId} missing an ID, cannot update for invoice rename:`, item);
          }
        }
      }
    } catch (error) {
      throw new Error(
        `Failed to rename invoice from ${oldInvoiceId} to ${newInvoiceId} for PO ${poId} and update items: ${getErrorMessage(error)}`
      );
    }
  }
}

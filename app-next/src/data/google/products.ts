// "use server"
import { AbstractFirestoreService } from './firestore/AbstractFirestoreService';
import { getInventoryAnalyis, InventoryAnalysisResult } from './bigQueryRest';
import { EnrichedInventoryAnalysisResultItem } from './types';

export type Product = {
  id: string;
  sku: string;
  microcenterUpc?: string;
  inventory?: EnrichedInventoryAnalysisResultItem;
  skuMaps?: string[];
  dateAdded?: Date;
  shopify?: any;
  upc?: string;
}

export class Products {
  products: Product[] = [];
  inventoryAnalysis: InventoryAnalysisResult | null = null;

  constructor(private firebase: AbstractFirestoreService) {
  }

  async init(options: { inventoryAnalysis: Date | null } = { inventoryAnalysis: null }) {
    // FirebaseLib will automatically convert timestamps to dates
    this.products = await this.firebase.getAll('products');
    if (options.inventoryAnalysis) {
      this.inventoryAnalysis = await getInventoryAnalyis(options.inventoryAnalysis);
      this.#mapInventoryAnalysis();
    }
  }

  mapProductSkus(skus: string[]) {
    const mappedProducts = skus.map(sku => this.mapProductSku(sku));
    return mappedProducts;
  }

  mapProductSku(sku: string) {
    return this.products.find((product: any) => product.sku === sku || product.skuMaps?.includes(sku));
  }

  #mapInventoryAnalysis = () => {
    if (!this.inventoryAnalysis) {
      return;
    }
    const { inventoryData, dates } = this.inventoryAnalysis;
    for (const i of inventoryData) {
      if (i.color) {
        const product = this.mapProductSku(i.color);
        if (product) {
          product.inventory = i;
        }
      }
    }
  }

  async deleteProduct(id: string) {
    // Remove from products array
    await this.firebase.deleteDoc('products', id);
    this.products = this.products.filter((product: any) => product.id !== id);
  }
}

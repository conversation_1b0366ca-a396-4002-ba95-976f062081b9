import admin from "firebase-admin";
import { authConfig } from "../../config/server-config";
import { FirestoreServerService } from "./firestore/FirestoreServerService";

// Initialize or get the main Firebase app
export const firebaseApp = admin.apps?.[0] ||  admin.initializeApp({
  credential: admin.credential.cert(authConfig.serviceAccount),
});

// Create and export the new FirestoreServerService instance
const firebase = new FirestoreServerService();

export default firebase;

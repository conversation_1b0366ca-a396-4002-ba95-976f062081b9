"client-only";

import { 
  Firestore, 
  Timestamp, 
  getFirestore, 
  doc, 
  getDoc, 
  collection, 
  getDocs, 
  updateDoc, 
  setDoc, 
  addDoc, 
  deleteDoc as deleteDocFirestore,
  query, 
  where, 
  collectionGroup,
  writeBatch,
  deleteField,
  WhereFilterOp,
  QuerySnapshot,
  QueryDocumentSnapshot,
  DocumentSnapshot
} from "firebase/firestore";
import { AbstractFirestoreService } from './AbstractFirestoreService';
import { getFirebaseApp } from '~/auth/firebase';

export class FirestoreClientService extends AbstractFirestoreService {
  public firestore: Firestore;

  constructor() {
    super();
    const app = getFirebaseApp();
    this.firestore = getFirestore(app);
  }

  // --- Implementation of abstract CRUD methods ---

  async get<T>(collectionName: string, id: string): Promise<T | null> {
    const docRef = doc(this.firestore, collectionName, id);
    const docSnap = await getDoc(docRef);
    return docSnap.exists() ? this.processDoc(docSnap) : null;
  }

  async getAll<T>(name: string, id?: string, subName?: string): Promise<T[]> {
    console.log("getAll", name, id, subName);
    const ref = id && subName 
      ? collection(this.firestore, name, id, subName) 
      : collection(this.firestore, name);
    const querySnapshot = await getDocs(ref);
    return this.processDocs(querySnapshot);
  }

  async update<T>(name: string, id: string, data: Partial<T>): Promise<any> {
    const convertedData = this.convertDatesToTimestamps(data);
    const docRef = doc(this.firestore, name, id);
    return await updateDoc(docRef, convertedData);
  }

  async set<T>(name: string, id: string, data: Partial<T>): Promise<any> {
    const newData = this.convertDatesToTimestamps(data);
    console.log(`set (client: true)`, name, id, newData);
    try {
      const docRef = doc(this.firestore, name, id);
      const result = await setDoc(docRef, newData, { merge: true });
      return { success: true, result };
    } catch (error) {
      console.error("Error setting document: ", error);
      return { success: false, error };
    }
  }

  async add<T>(name: string, data: Partial<T>): Promise<any> {
    const convertedData = this.convertDatesToTimestamps(data);
    const ref = collection(this.firestore, name);
    return await addDoc(ref, convertedData);
  }

  async deleteDoc(name: string, id: string): Promise<any> {
    const ref = doc(this.firestore, name, id);
    return await deleteDocFirestore(ref);
  }

  async queryCollection<T>(name: string, queryConditions: [string, WhereFilterOp, any][]): Promise<T[]> {
    const ref = collection(this.firestore, name);
    const q = query(ref, ...queryConditions.map(i => where(...i)));
    const querySnapshot = await getDocs(q);
    return this.processDocs(querySnapshot);
  }

  async queryCollectionGroup<T>(name: string, queryConditions: [string, WhereFilterOp, any][]): Promise<T[]> {
    const ref = collectionGroup(this.firestore, name);
    const q = query(ref, ...queryConditions.map(i => where(...i)));
    const querySnapshot = await getDocs(q);
    return this.processDocs(querySnapshot);
  }

  async batchSet<T>(name: string, data: T[], batchSize: number = 0): Promise<any> {
    if (batchSize > 0) {
      const batches = [];
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        batches.push(this.batchSet(name, batch));
      }
      return Promise.all(batches);
    }
    
    const batch = writeBatch(this.firestore);
    data.forEach(d => {
      const docRef = doc(this.firestore, name, (d as any).id);
      batch.set(docRef, this.convertDatesToTimestamps(d));
    });
    return await batch.commit();
  }

  async updateSubcollection<T>(name: string, id: string, subName: string, subId: string, data: Partial<T>): Promise<any> {
    const convertedData = this.convertDatesToTimestamps(data);
    const docRef = doc(this.firestore, name, id, subName, subId);
    return await updateDoc(docRef, convertedData);
  }

  async setSubcollection<T>(name: string, id: string, subName: string, data: Partial<T>, subId?: string): Promise<string> {
    try {
      const convertedData = this.convertDatesToTimestamps(data);
      if (subId) {
        const ref = doc(collection(this.firestore, name, id, subName), subId);
        await setDoc(ref, convertedData, { merge: true });
        return subId;
      } else {
        const collectionRef = collection(this.firestore, name, id, subName);
        const docRef = await addDoc(collectionRef, convertedData);
        return docRef.id;
      }
    } catch (error: unknown) {
      console.error("Error in setSubcollection:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to set subcollection: ${errorMessage}`);
    }
  }

  // --- Implementation of abstract utility methods ---

  convertDatesToTimestamps(data: any): any {
    if (data === null || typeof data !== 'object') {
      return data;
    }

    // 1. Preserve FieldValue sentinels (e.g., deleteField, serverTimestamp)
    // These are identified by having a _methodName property.
    if (typeof data._methodName === 'string') {
      return data;
    }

    // 2. Handle JavaScript Date objects
    if (data instanceof Date) {
      return Timestamp.fromDate(data);
    }

    // 3. Handle Firestore Timestamp objects
    if (data instanceof Timestamp) {
      return data;
    }
    
    // If it's not the environment's Timestamp type, but looks like a generic Firestore Timestamp
    if (
      typeof data.seconds === 'number' &&
      typeof data.nanoseconds === 'number' &&
      typeof data.toDate === 'function'
    ) {
      return new Timestamp(data.seconds, data.nanoseconds);
    }

    // 4. Recursive step for arrays and plain objects
    const recursivelyConvertedData: any = Array.isArray(data) ? [] : {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        recursivelyConvertedData[key] = this.convertDatesToTimestamps(data[key]);
      }
    }
    return recursivelyConvertedData;
  }

  deleteField() {
    return deleteField();
  }

  // --- Private helper methods ---

  private timestampToDate(timestamp: Timestamp): Date {
    const seconds = timestamp.seconds;
    if (typeof seconds !== 'number') {
      console.error('Invalid timestamp format:', timestamp);
      throw new Error(`Invalid timestamp format: ${timestamp}`);
    }
    return new Date(seconds * 1000);
  }

  protected processDocs<T>(docs: QuerySnapshot): T[] {
    const items: T[] = [];
    docs.forEach((doc) => {
      items.push(this.processDoc(doc));
    });
    return items;
  }

  protected processDoc<T>(doc: QueryDocumentSnapshot | DocumentSnapshot): T {
    const data = doc.data();
    if (!data) {
      throw new Error('Document data is undefined');
    }
    
    // Ensure id is always included and cannot be overwritten
    Object.defineProperty(data, 'id', {
      value: doc.id,
      enumerable: true,
      configurable: false,
      writable: false
    });
    
    return this.convertTimestamps(data) as T;
  }

  private convertTimestamps(data: any): any {
    if (data === null || typeof data !== 'object') {
      return data;
    }

    if (data instanceof Timestamp) {
      return this.timestampToDate(data);
    }

    const convertedData: any = Array.isArray(data) ? [] : {};
    for (const key in data) {
      convertedData[key] = this.convertTimestamps(data[key]);
    }
    return convertedData;
  }
}
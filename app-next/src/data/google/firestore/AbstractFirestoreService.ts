export type WhereFilterOp = '==' | '!=' | '<' | '<=' | '>' | '>=' | 'array-contains' | 'in' | 'array-contains-any' | 'not-in';

export interface RestockReport {
  report: {
    [key: string]: any;
  }
  reportData: ReportItem[];
}

export interface ReportItem {
  country: string;
  productName: string;
  fnsku: string;
  merchantSKU: string;
  asin: string;
  condition: string;
  supplier: string;
  supplierPartNo?: null;
  currencyCode: string;
  price: number;
  salesLast30Days: number;
  unitsSoldLast30Days: number;
  totalUnits: number;
  inbound: number;
  available: number;
  fcTransfer: number;
  fcProcessing: number;
  customerOrder: number;
  unfulfillable: number;
  working: number;
  shipped: number;
  receiving: number;
  fulfilledBy: string;
  totalDaysOfSupplyIncludingUnitsFromOpenShipments?: number | null;
  daysOfSupplyAtAmazonFulfillmentNetwork?: number | null;
  alert: string;
  recommendedReplenishmentQty: number;
  recommendedShipDate: string;
  recommendedAction: string;
  unitStorageSize: string;
}

export abstract class AbstractFirestoreService {
  // --- Abstract CRUD Methods ---
  abstract get<T>(collection: string, id: string): Promise<T | null>;
  abstract getAll<T>(collection: string, id?: string, subName?: string): Promise<T[]>;
  abstract update<T>(name: string, id: string, data: Partial<T>): Promise<any>;
  abstract set<T>(name: string, id: string, data: Partial<T>): Promise<{ success: boolean; result?: any; error?: any; }>;
  abstract add<T>(name: string, data: Partial<T>): Promise<any>;
  abstract deleteDoc(name: string, id: string): Promise<any>;
  abstract queryCollection<T>(name: string, queryConditions: [string, WhereFilterOp, any][]): Promise<T[]>;
  abstract queryCollectionGroup<T>(name: string, queryConditions: [string, WhereFilterOp, any][]): Promise<T[]>;
  abstract batchSet<T>(name: string, data: T[], batchSize?: number): Promise<any>;
  abstract updateSubcollection<T>(name: string, id: string, subName: string, subId: string, data: Partial<T>): Promise<any>;
  abstract setSubcollection<T>(name: string, id: string, subName: string, data: Partial<T>, subId?: string): Promise<string>;

  // --- Abstract Utility Methods ---
  abstract convertDatesToTimestamps(data: any): any;
  abstract deleteField(): any;

  // --- Abstract Data Processing Methods ---
  protected abstract processDoc<T>(doc: any): T;
  protected abstract processDocs<T>(docs: any): T[];

  // --- Concrete Business Logic Methods ---
  async getFilamentPurchases<T = any>(): Promise<T[]> {
    return this.getAll<T>("filament-purchases");
  }

  async updateFilamentPurchase<T = any>(id: string, data: Partial<T>) {
    return this.update<T>("filament-purchases", id, data);
  }

  async getFilamentPurchasesOnTheWay<T = any>(): Promise<T[]> {
    let purchases = await this.queryCollection<T>("filament-purchases", [["on-the-way", "==", true]]);
    for (let purchase of purchases) {
      let items = await this.getAll("filament-purchases", (purchase as any).id, "Items");
      (purchase as any).items = items;
    }
    return purchases;
  }

  async getFilamentPurchaseItems<T = any>(id: string): Promise<T[]> {
    return this.getAll<T>("filament-purchases", id, "Items");
  }
  
  async updateFilamentPurchaseItem<T = any>(purchaseId: string, itemId: string, data: Partial<T>) {
    return this.updateSubcollection<T>("filament-purchases", purchaseId, "Items", itemId, data);
  }
  
  async getProducts<T = any>(): Promise<T[]> {
    return this.getAll<T>("products");
  }
  
  async updateProduct<T = any>(id: string, data: Partial<T>) {
    return this.update<T>("products", id, data);
  }

  async getRestockReport(): Promise<RestockReport> {
    return this.get<RestockReport>("reports", 'restock-report') as Promise<RestockReport>;
  }

  async getAmazonListings<T = any>(): Promise<T[]> {
    return this.getAll<T>("amazon-listings");
  }

  async updateAmazonListing<T = any>(id: string, data: Partial<T>) {
    return this.update<T>("amazon-listings", id, data);
  }

  // Generic functions
  async getData<T = any>(tableName: string, subName?: string, id?: string): Promise<T[]> {
    if (id && subName)
      return this.getAll<T>(tableName, id, subName);
    else
      return this.getAll<T>(tableName);
  }

  async updateData<T = any>(tableName: string, id: string, data: Partial<T>) {
    return this.update<T>(tableName, id, data);
  }

  async setData<T = any>(tableName: string, id: string, data: Partial<T>) {
    return this.set<T>(tableName, id, data);
  }

  async addData<T = any>(tableName: string, data: Partial<T>) {
    return this.add<T>(tableName, data);
  }

  async setSubData<T = any>(tableName: string, subName: string, id: string, data: Partial<T>, subId?: string) {
    try {
      if (!tableName || !subName || !id || !data) {
        throw new Error(`Invalid parameters: tableName=${tableName}, subName=${subName}, id=${id}, data=${!!data}`);
      }

      const result = await this.setSubcollection<T>(tableName, id, subName, data, subId);
      if (!result) {
        throw new Error('setSubcollection returned no result');
      }

      return result;
    } catch (error: unknown) {
      console.error("Error in setSubData:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to set subcollection data: ${errorMessage}`);
    }
  }
}
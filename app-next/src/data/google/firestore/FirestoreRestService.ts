import { AbstractFirestoreService, WhereFilterOp } from './AbstractFirestoreService';
import * as api from '../firestore-REST';

export class NotImplementedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotImplementedError';
  }
}

export class FirestoreRestService extends AbstractFirestoreService {
  private idToken: string;

  constructor(idToken: string) {
    super();
    this.idToken = idToken;
  }

  // --- Implementation of abstract methods ---

  async get<T>(collection: string, id: string): Promise<T | null> {
    return api.getDoc<T>(collection, id, this.idToken);
  }

  async getAll<T>(collection: string, id?: string, subName?: string): Promise<T[]> {
    let collectionPath: string;
    
    if (id && subName) {
      // This is a subcollection: collection/id/subName
      collectionPath = api.createCollectionPath([collection, id, subName]);
    } else {
      // This is a top-level collection
      collectionPath = collection;
    }
    
    return api.getDocs<T>(collectionPath, this.idToken);
  }

  async update<T>(name: string, id: string, data: Partial<T>): Promise<any> {
    throw new NotImplementedError('update is not implemented for FirestoreRestService. The REST API only supports full document replacement via setDoc. Use set() instead for full document updates.');
  }

  async set<T>(name: string, id: string, data: Partial<T>): Promise<any> {
    await api.setDoc(name, id, data, this.idToken);
    return data;
  }

  async add<T>(name: string, data: Partial<T>): Promise<any> {
    throw new NotImplementedError('add is not implemented for FirestoreRestService. The REST API does not support auto-generated document IDs. Use set() with a specific ID instead.');
  }

  async deleteDoc(name: string, id: string): Promise<any> {
    throw new NotImplementedError('deleteDoc is not implemented for FirestoreRestService. The REST API only supports collection-level deletion via deleteCollection().');
  }

  async queryCollection<T>(name: string, queryConditions: [string, WhereFilterOp, any][]): Promise<T[]> {
    return api.queryDocs<T>(name, queryConditions, this.idToken);
  }

  async queryCollectionGroup<T>(name: string, queryConditions: [string, WhereFilterOp, any][]): Promise<T[]> {
    throw new NotImplementedError('queryCollectionGroup is not implemented for FirestoreRestService. Collection group queries are not supported by the Firestore REST API.');
  }

  async batchSet<T>(name: string, data: T[], batchSize?: number): Promise<any> {
    throw new NotImplementedError('batchSet is not implemented for FirestoreRestService. Batch operations are not available in the current REST API implementation.');
  }

  async updateSubcollection<T>(name: string, id: string, subName: string, subId: string, data: Partial<T>): Promise<any> {
    throw new NotImplementedError('updateSubcollection is not implemented for FirestoreRestService. The REST API only supports full document replacement, not partial updates. Use setSubcollection() instead for full document updates.');
  }

  async setSubcollection<T>(name: string, id: string, subName: string, data: Partial<T>, subId?: string): Promise<string> {
    if (!subId) {
      throw new NotImplementedError('setSubcollection requires a subId for FirestoreRestService. The REST API does not support auto-generated document IDs.');
    }
    
    const collectionPath = api.createCollectionPath([name, id, subName]);
    await api.setDoc(collectionPath, subId, data, this.idToken);
    return subId;
  }

  // --- Abstract Utility Methods ---

  convertDatesToTimestamps(data: any): any {
    // For the REST API, date conversion is handled automatically by the toFirestoreValue function
    // in firestore-REST.ts, so we can return the data as-is
    return data;
  }

  deleteField(): any {
    throw new NotImplementedError('deleteField is not implemented for FirestoreRestService. Field-level deletion is not supported by the current REST API implementation.');
  }

  // --- Data Processing Methods ---
  // For REST service, data processing is handled by the REST API itself
  
  protected processDoc<T>(doc: any): T {
    // The REST API already returns processed documents, so we just return as-is
    return doc as T;
  }

  protected processDocs<T>(docs: any): T[] {
    // The REST API already returns processed documents, so we just return as-is
    return docs as T[];
  }
}
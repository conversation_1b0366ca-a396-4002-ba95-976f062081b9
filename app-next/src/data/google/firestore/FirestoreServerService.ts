import { Firestore, Timestamp, FieldValue, getFirestore } from 'firebase-admin/firestore';
import { AbstractFirestoreService, type WhereFilterOp } from './AbstractFirestoreService';
import { firebaseApp } from '../firebase-server';

export class FirestoreServerService extends AbstractFirestoreService {
  private firestore: Firestore;

  constructor() {
    super();
    this.firestore = getFirestore(firebaseApp);
  }

  // --- CRUD Method Implementations ---

  async get<T>(collection: string, id: string): Promise<T | null> {
    const ref = this.firestore.collection(collection).doc(id);
    const doc = await ref.get();
    return doc.exists ? this.processDoc<T>(doc) : null;
  }

  async getAll<T>(collection: string, id?: string, subName?: string): Promise<T[]> {
    console.log("getAll", collection, id, subName);
    const ref = id && subName 
      ? this.firestore.collection(collection).doc(id).collection(subName)
      : this.firestore.collection(collection);
    const querySnapshot = await ref.get();
    return this.processDocs<T>(querySnapshot);
  }

  async update<T>(name: string, id: string, data: Partial<T>): Promise<any> {
    const convertedData = this.convertDatesToTimestamps(data);
    const docRef = this.firestore.collection(name).doc(id);
    return await docRef.update(convertedData);
  }

  async set<T>(name: string, id: string, data: Partial<T>): Promise<any> {
    const newData = this.convertDatesToTimestamps(data);
    console.log(`set (server)`, name, id, newData);
    try {
      const docRef = this.firestore.collection(name).doc(id);
      const result = await docRef.set(newData, { merge: true });
      return { success: true, result };
    } catch (error) {
      console.error("Error setting document: ", error);
      return { success: false, error };
    }
  }

  async add<T>(name: string, data: Partial<T>): Promise<any> {
    const convertedData = this.convertDatesToTimestamps(data);
    const ref = this.firestore.collection(name);
    return await ref.add(convertedData);
  }

  async deleteDoc(name: string, id: string): Promise<any> {
    const ref = this.firestore.collection(name).doc(id);
    return await ref.delete();
  }

  async queryCollection<T>(name: string, queryConditions: [string, WhereFilterOp, any][]): Promise<T[]> {
    const ref = this.firestore.collection(name);
    const q = queryConditions.reduce<FirebaseFirestore.Query<FirebaseFirestore.DocumentData>>(
      (acc, [fieldPath, opStr, value]) => acc.where(fieldPath, opStr, value),
      ref
    );
    const querySnapshot = await q.get();
    return this.processDocs<T>(querySnapshot);
  }

  async queryCollectionGroup<T>(name: string, queryConditions: [string, WhereFilterOp, any][]): Promise<T[]> {
    const ref = this.firestore.collectionGroup(name);
    const q = queryConditions.reduce<FirebaseFirestore.Query<FirebaseFirestore.DocumentData>>(
      (acc, [fieldPath, opStr, value]) => acc.where(fieldPath, opStr, value),
      ref
    );
    const querySnapshot = await q.get();
    return this.processDocs<T>(querySnapshot);
  }

  async batchSet<T>(name: string, data: T[], batchSize: number = 0): Promise<any> {
    if (batchSize > 0) {
      const batches = [];
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        batches.push(this.batchSet(name, batch));
      }
      return Promise.all(batches);
    }

    const batch = this.firestore.batch();
    data.forEach(d => {
      console.log("batchSet", name, (d as any).id);
      const ref = this.firestore.collection(name).doc((d as any).id);
      batch.set(ref, this.convertDatesToTimestamps(d));
    });
    return await batch.commit();
  }

  async updateSubcollection<T>(name: string, id: string, subName: string, subId: string, data: Partial<T>): Promise<any> {
    const convertedData = this.convertDatesToTimestamps(data);
    const docRef = this.firestore.collection(name).doc(id).collection(subName).doc(subId);
    return await docRef.update(convertedData);
  }

  async setSubcollection<T>(name: string, id: string, subName: string, data: Partial<T>, subId?: string): Promise<string> {
    try {
      const convertedData = this.convertDatesToTimestamps(data);
      if (subId) {
        const ref = this.firestore.collection(name).doc(id).collection(subName).doc(subId);
        await ref.set(convertedData, { merge: true });
        return subId;
      } else {
        const ref = this.firestore.collection(name).doc(id).collection(subName);
        const docRef = await ref.add(convertedData);
        if (!docRef) {
          throw new Error('No document reference returned from add operation');
        }
        return docRef.id;
      }
    } catch (error: unknown) {
      console.error("Error in setSubcollection:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to set subcollection: ${errorMessage}`);
    }
  }

  // --- Utility Method Implementations ---

  convertDatesToTimestamps(data: any): any {
    if (data === null || typeof data !== 'object') {
      return data;
    }

    // 1. Preserve FieldValue sentinels (e.g., deleteField, serverTimestamp)
    if (typeof data._methodName === 'string') {
      return data;
    }

    // 2. Handle JavaScript Date objects
    if (data instanceof Date) {
      return Timestamp.fromDate(data);
    }

    // 3. Handle Firestore Timestamp objects
    if (data instanceof Timestamp) {
      return data;
    }
    
    // If it's not the environment's Timestamp type, but looks like a generic Firestore Timestamp
    if (
      typeof data.seconds === 'number' &&
      typeof data.nanoseconds === 'number' &&
      typeof data.toDate === 'function'
    ) {
      return new Timestamp(data.seconds, data.nanoseconds);
    }

    // 4. Recursive step for arrays and plain objects
    const recursivelyConvertedData: any = Array.isArray(data) ? [] : {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        recursivelyConvertedData[key] = this.convertDatesToTimestamps(data[key]);
      }
    }
    return recursivelyConvertedData;
  }

  deleteField() {
    return FieldValue.delete();
  }

  // --- Private Helper Methods ---

  protected processDocs<T>(docs: FirebaseFirestore.QuerySnapshot<FirebaseFirestore.DocumentData>): T[] {
    const items: T[] = [];
    docs.forEach((doc) => {
      items.push(this.processDoc<T>(doc));
    });
    return items;
  }

  protected processDoc<T>(doc: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData> | FirebaseFirestore.DocumentSnapshot<FirebaseFirestore.DocumentData>): T {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }
    
    // Ensure id is always included and cannot be overwritten
    Object.defineProperty(data, 'id', {
      value: doc.id,
      enumerable: true,
      configurable: false,
      writable: false
    });
    
    const result = this.convertTimestamps(data);
    return result as T;
  }

  private convertTimestamps(data: any): any {
    if (data === null || typeof data !== 'object') {
      return data;
    }

    if (data instanceof Timestamp) {
      return this.timestampToDate(data);
    }

    const convertedData: any = Array.isArray(data) ? [] : {};
    for (const key in data) {
      convertedData[key] = this.convertTimestamps(data[key]);
    }
    return convertedData;
  }

  private timestampToDate(timestamp: Timestamp): Date {
    const seconds = timestamp.seconds;
    if (typeof seconds !== 'number') {
      console.error('Invalid timestamp format:', timestamp);
      throw new Error(`Invalid timestamp format: ${timestamp}`);
    }
    return new Date(seconds * 1000);
  }
}
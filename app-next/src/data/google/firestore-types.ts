import type { PurchaseOrderStatus } from "~/data/purchase-orders/types"; 
export type { PurchaseOrderStatus } from "~/data/purchase-orders/types"; 


export enum InvoiceStatus {
  NEW = "NEW",
  PAID = "PAID",
  PARTIALLY_PAID = "PARTIALLY_PAID",
  VOID = "VOID"
}

export interface PurchaseOrderItem {
  id: string;
  sku: string;
  quantity: number;
  amount: number;
  outstanding: number;
  invoicedQuantity: number;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
  }
}

export interface XeroLineItem {
  lineItemID?: string;
  description?: string;
  lineAmount?: number;
  accountCode?: string;
  }

export interface RestockReport {
  report: {
    [key: string]: any;
  }
  reportData: ReportItem[];
}
export interface ReportItem {
  country: string;
  productName: string;
  fnsku: string;
  merchantSKU: string;
  asin: string;
  condition: string;
  supplier: string;
  supplierPartNo?: null;
  currencyCode: string;
  price: number;
  salesLast30Days: number;
  unitsSoldLast30Days: number;
  totalUnits: number;
  inbound: number;
  available: number;
  fcTransfer: number;
  fcProcessing: number;
  customerOrder: number;
  unfulfillable: number;
  working: number;
  shipped: number;
  receiving: number;
  fulfilledBy: string;
  totalDaysOfSupplyIncludingUnitsFromOpenShipments?: number | null;
  daysOfSupplyAtAmazonFulfillmentNetwork?: number | null;
  alert: string;
  recommendedReplenishmentQty: number;
  recommendedShipDate: string;
  recommendedAction: string;
  unitStorageSize: string;
}

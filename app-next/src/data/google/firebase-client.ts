import "client-only"; // Ensure this file is only run on the client side

// import firebase from "firebase/compat/app";
// import { initializeApp } from "firebase/app";
// import { FirebaseApp } from "firebase/app";
import { FirestoreClientService } from './firestore/FirestoreClientService';

const firebaseConfig = {
  apiKey: "AIzaSyDC1lvH8i6eASLcQj6mnppcReVmQm0sEsY",
  authDomain: "cookiecad-project.firebaseapp.com",
  projectId: "cookiecad-project",
  storageBucket: "cookiecad-project.appspot.com",
  messagingSenderId: "300727719618",
  appId: "1:300727719618:web:d9283be29491a085b68d5b",
  measurementId: "G-SG39Z3WHMS"
}

// const app = firebase.initializeApp(firebaseConfig);
// console.log("firebase", firebase)

function firebaseLib(): FirestoreClientService {
  return new FirestoreClientService();
}

const firebaseClient = firebaseLib();
export default firebaseClient;
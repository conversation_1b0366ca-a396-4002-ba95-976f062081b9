import { AbstractFirestoreService } from './firestore/AbstractFirestoreService';
import { Products } from './products';
import * as shopifyGraphQl from '~/data/shopify/shopifyGrapql';

//.cancelled_at is null
//current_total_price
const startAtDate = '2023-01-01'
const ignoredTags = ['custom-cookie-cutter', 'judaica', 'vendor-shop', 'lil-miss-cakes-cutters', 'heat-sealer-table', 'bulk'];

export async function getShopifyOrders(firebaseClient: AbstractFirestoreService, startAtDate: string) {
  let orders = await firebaseClient.queryCollection<any>('shopify-orders', [['processed_at', '>', startAtDate]]);
  //filter out orders with ignored tags
  orders = orders.filter((order: any) => 
    (order.tags?.toLowerCase().split(',') || []).every((tag: string) => !ignoredTags.includes(tag.trim())))

  //Separate order ids into groups of 30
  // let orderIds = orders.map((order: any) => order.id.toString());
  // let orderGroups = [];
  // while (orderIds.length > 0) {
  //   orderGroups.push(orderIds.splice(0, 30));
  // }
  // for (const orderGroup of orderGroups) {
    // let lineItems = await firebaseClient.queryCollectionGroup('shopify-order-line-items', ['orderId', 'in', orderGroup]);
  for (const order of orders) {
    for (const lineItem of order.lineItems) {
      // let order = orders.find((order: any) => order.id === lineItem.orderId);
      // if (!order.lineItems) {
      //   order.lineItems = [];
      // }

      //Calculate the remaining quantity of the line item
      let refundQuantity = order.refunds.reduce((acc: number, refund: any) => {
        return acc + refund.refund_line_items.reduce((acc: number, refundLineItem: any) => {
          if (refundLineItem.line_item_id !== lineItem.id) { return acc; }
          else { return acc + refundLineItem.quantity; }
        }, 0);
      }, 0);
      lineItem.refundedQuantity = refundQuantity;
    }
  }
  return orders;
}

export type shopifyOrderMonthlyReport = {
  [yearMonth: string]: {
    skus: {
      [sku: string]: {
        totalQuantity: number;
        quantityRefunded: number;
        tags: string[];
        fullCasesSold: number;
      }
    };
    amount: number;
  }
}

export async function generateShopifyOrderMonthly(firebaseClient: AbstractFirestoreService) {
  const products = new Products(firebaseClient);
  await products.init();
  const orders = await getShopifyOrders(firebaseClient, startAtDate);
  let report: shopifyOrderMonthlyReport = {};
  for (const order of orders || []) {
    //skip cancelled orders
    if (order.cancelled_at) { continue; }

    let date = new Date(order.processed_at);
    let month = date.getMonth() + 1;
    let year = date.getFullYear();
    let key = `${year}-${month}`;
    report[key] = report[key] || { skus: {}, amount: 0 };
    for (const lineItem of order.lineItems) {
      if (lineItem.sku?.endsWith('-12')) {
        lineItem.sku = lineItem.sku.replace('-12', '');
        lineItem.case = true;
      }
      let cookiecadSku = products.mapProductSku(lineItem.sku)?.sku || lineItem.sku || lineItem.title || lineItem.variant_title || 'unknown';
      if (!report[key].skus[cookiecadSku]) {
        report[key].skus[cookiecadSku] = {
          totalQuantity: 0,
          quantityRefunded: 0,
          tags: [],
          fullCasesSold: 0
        };
      }
      if (lineItem.case) {
        report[key].skus[cookiecadSku].fullCasesSold += lineItem.quantity - lineItem.refundedQuantity;
        lineItem.quantity *= 12;
        lineItem.refundedQuantity *= 12;
      }
      report[key].skus[cookiecadSku].totalQuantity += lineItem.quantity - lineItem.refundedQuantity;
      report[key].skus[cookiecadSku].quantityRefunded += lineItem.refundedQuantity;
      report[key].skus[cookiecadSku].tags = [...new Set([...(report[key].skus[cookiecadSku].tags || []), ...(order.tags?.split(',') || [])])];
    }
    report[key].amount += parseFloat(order.current_total_price);
  }
  await firebaseClient.set('data', 'shopifyOrdersMonthly', report);

  return report;
}

export async function generateShopifyOrderLineItems(firebaseClient: AbstractFirestoreService) {
  const orders = await getShopifyOrders(firebaseClient, '2023-01-01');
  let lineItems = [];
  for (const order of orders) {
    for (const lineItem of order.lineItems) {
      lineItems.push({
        id: `${order.order_number}-${lineItem.id}`,
        orderId: order.id,
        created_at: order.created_at,
        processed_at: order.processed_at,
        cancelled_at: order.cancelled_at,
        currency: order.currency,
        current_total_price: parseFloat(order.current_total_price),
        customer: order.customer 
        ? {
          id: order.customer.id,
          email: order.customer.email,
        } 
        : null,
        tags: order.tags.split(',') || [],
        order_number: order.order_number,
        lineItem: lineItem
      });
    }
  }
  // console.log(lineItems);
  //Call in batches of 500
  let batch = [];
  for (let i = 0; i < lineItems.length; i += 500) {
    batch.push(lineItems.slice(i, i + 500));
  }
  console.log('batch', batch.length);
  for (const batchItem of batch) {
    console.log('batchItem', batchItem.length);
    firebaseClient.batchSet('shopifyOrderLineItems', batchItem);
  }

  //await firebaseClient.batchSet('shopifyOrderLineItems', lineItems);
}

export async function pushOrdersWithTransactions(firebaseClient: AbstractFirestoreService) {
  let orders = await shopifyGraphQl.getOrdersWithTransactionsFromDate('2024-01-01');
  console.log(`Pushing ${orders.length} orders`);
  //remove # from order name
  orders = orders.map((order: any) => ({ ...order, 
    orderId: order.id, 
    id: order.name.replace('#', '')}));
  // console.log(orders);
  await firebaseClient.batchSet('shopify-orders-tx', orders, 50);
}

export async function addLineItemsToOrders(firebaseClient: AbstractFirestoreService) {
  let orders = await firebaseClient.getAll<any>('shopify-orders');
  let promises = [];
  for (const order of orders) {
    promises.push(firebaseClient.getAll('shopify-orders', order.id.toString(), 'line-items').then((lineItems: any) => {
      let orderData = {
        lineItems: lineItems
      }
      firebaseClient.update('shopify-orders', order.id.toString(), orderData);
    }));
  }
  await Promise.all(promises);
  return orders;
}



/*
with refund as (  
  select o1.orderId, 
         JSON_VALUE(refund_li.line_item.variant_id) variant_id, 
         INT64(refund_li.quantity) quantity,
         INT64(refund_li.line_item_id) li_id,
         refund_li refund_data
  from  `cookiecad-project.cookiecad_data_analysis.shopify-orders-view` o1
  join unnest(JSON_EXTRACT_ARRAY(o1.shopifyOrderData.refunds)) refunds
  join unnest(JSON_EXTRACT_ARRAY(refunds.refund_line_items)) refund_li
),

lineItems as (
  select o.*, li.name, li.sku, li.title, li.variant_title, li.quantity li_quantity,li.variant_id,
        INT64(li.id) li_id,
        li li_data
  from `cookiecad-project.cookiecad_data_analysis.shopify-orders-view` o
  left join unnest(JSON_EXTRACT_ARRAY(shopifyOrderData.line_items)) li
)

select li.*,
  INT64(li.li_quantity) - COALESCE(refund.quantity, 0) as quantity, 
  refund.quantity refunded,
  `cookiecad-project.cookiecad_data_analysis.sku_map`(COALESCE(JSON_VALUE(li.sku), JSON_VALUE(li.title))) color, 
  refund_data
from lineItems li
left join refund on (refund.orderId = li.orderId AND (refund.li_id = li.li_id))

*/
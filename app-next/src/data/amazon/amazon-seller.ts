// import * as dotenv from 'dotenv';
import fs from 'fs';
import { fileURLToPath } from 'url';
import path, { dirname } from 'path';
import { inspect } from 'util';
import { deepMergeEmpty } from '~/lib/utils';

import * as amazonSpApi from './amazon-sp-api-aws-tools';
import env from '~/env';

// // Load the default .env file
// dotenv.config();
// // Load the .env.awsaccess file
// dotenv.config({ path: './.env.awsaccess' });
const naRefreshToken = env.SELLING_PARTNER_REFRESH_TOKEN;
const feRefreshToken =  '' //env.FE_AWS_REFRESH_TOKEN;

import firebase from '~/data/google/firebase-server';

import { SellingPartnerApiAuth } from '@sp-api-sdk/auth';
import { FulfillmentOutboundApiClient } from '@sp-api-sdk/fulfillment-outbound-api-2020-07-01';
import { ProductTypeDefinitionsApiClient } from '@sp-api-sdk/product-type-definitions-api-2020-09-01';
import { ListingsItemsApiClient, PatchOperationOpEnum } from '@sp-api-sdk/listings-items-api-2021-08-01';
import { FbaInventoryApiClient } from '@sp-api-sdk/fba-inventory-api-v1';
import { CatalogItemsApiClient, GetCatalogItemIncludedDataEnum } from '@sp-api-sdk/catalog-items-api-2022-04-01';
import { parentListingAttributesTemplate } from './parentListingTemplate';
import { ReportsApiClient } from '@sp-api-sdk/reports-api-2021-06-30';
import { OrdersApiClient } from '@sp-api-sdk/orders-api-v0';
import * as scaleLeap from './amazon-sp-api-scaleleap';
import { requiredAttributesRegional } from './filaments';
import { SellingPartnerRegion } from '@sp-api-sdk/common';
import { GetListingsItemIncludedDataEnum } from '@sp-api-sdk/listings-items-api-2021-08-01';

// Type definitions for Amazon API responses

// Interface for Amazon listing data stored in Firestore
export interface AmazonListing {
  listingDetails?: {
    attributes?: Record<string, any>;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface AmazonOrder {
  AmazonOrderId: string;
  PurchaseDate: string;
  OrderStatus: string;
  LastUpdateDate?: string;
  SalesChannel?: string;
  OrderChannel?: string;
  ShipServiceLevel?: string;
  OrderTotal?: {
    CurrencyCode: string;
    Amount: string;
  };
  NumberOfItemsShipped?: number;
  NumberOfItemsUnshipped?: number;
  PaymentExecutionDetail?: any[];
  PaymentMethod?: string;
  PaymentMethodDetails?: string[];
  MarketplaceId?: string;
  ShipmentServiceLevelCategory?: string;
  OrderType?: string;
  EarliestShipDate?: string;
  LatestShipDate?: string;
  EarliestDeliveryDate?: string;
  LatestDeliveryDate?: string;
  IsBusinessOrder?: boolean;
  IsPrime?: boolean;
  IsPremiumOrder?: boolean;
  IsGlobalExpressEnabled?: boolean;
  ReplacedOrderId?: string;
  IsReplacementOrder?: boolean;
  PromiseResponseDueDate?: string;
  IsEstimatedShipDateSet?: boolean;
  IsSoldByAB?: boolean;
  IsIBA?: boolean;
  DefaultShipFromLocationAddress?: any;
  BuyerInvoicePreference?: string;
  BuyerTaxInformation?: any;
  FulfillmentInstruction?: any;
  IsISPU?: boolean;
  IsAccessPointOrder?: boolean;
  MarketplaceTaxInfo?: any;
  SellerDisplayName?: string;
  ShippingAddress?: any;
  BuyerInfo?: any;
  AutomatedShippingSettings?: any;
  HasAutomatedShippingSettings?: boolean;
  ElectronicInvoiceStatus?: string;
}

export interface AmazonOrderItem {
  ASIN: string;
  OrderItemId: string;
  SellerSKU: string;
  Title: string;
  QuantityOrdered: number;
  QuantityShipped?: number;
  ProductInfo?: any;
  PointsGranted?: any;
  ItemPrice?: {
    CurrencyCode: string;
    Amount: string;
  };
  ShippingPrice?: {
    CurrencyCode: string;
    Amount: string;
  };
  ItemTax?: {
    CurrencyCode: string;
    Amount: string;
  };
  ShippingTax?: {
    CurrencyCode: string;
    Amount: string;
  };
  ShippingDiscount?: {
    CurrencyCode: string;
    Amount: string;
  };
  ShippingDiscountTax?: {
    CurrencyCode: string;
    Amount: string;
  };
  PromotionDiscount?: {
    CurrencyCode: string;
    Amount: string;
  };
  PromotionDiscountTax?: {
    CurrencyCode: string;
    Amount: string;
  };
  PromotionIds?: string[];
  CODFee?: {
    CurrencyCode: string;
    Amount: string;
  };
  CODFeeDiscount?: {
    CurrencyCode: string;
    Amount: string;
  };
  IsGift?: boolean;
  ConditionNote?: string;
  ConditionId?: string;
  ConditionSubtypeId?: string;
  ScheduledDeliveryStartDate?: string;
  ScheduledDeliveryEndDate?: string;
  PriceDesignation?: string;
  TaxCollection?: any;
  SerialNumberRequired?: boolean;
  IsTransparency?: boolean;
  IossNumber?: string;
  StoreChainStoreId?: string;
  DeemedResellerCategory?: string;
  BuyerInfo?: any;
  BuyerRequestedCancel?: any;
  SerialNumbers?: string[];
  AmazonOrderId: string; // Added for consistency with order processing
}

// Report status interface
export interface ReportStatus {
  reportId: string;
  reportType: string;
  processingStatus: string;
  marketplaceIds?: string[];
  reportDocumentId?: string;
  createdTime?: string;
  processingStartTime?: string;
  processingEndTime?: string;
}

// Settlement record interface for parsed data
export interface SettlementRecord {
  'settlement-id': string;
  'settlement-start-date': string;
  'settlement-end-date': string;
  'deposit-date': string;
  'total-amount': string;
  'currency': string;
  transactions: any[];
  totalPrincipal?: number;
}

// Amazon Pay Settlement record interface
export interface AmazonPaySettlementRecord {
  'settlement-id': string;
  transactions: any[];
  NetTransactionAmountTotal?: number;
  [key: string]: any;
}

const __dirname = dirname(fileURLToPath(import.meta.url));

// Rate limiting utility interfaces and functions
interface RetryOptions {
  maxRetries?: number;
  baseDelayMs?: number;
  maxDelayMs?: number;
  jitterFactor?: number;
  retryOn429Only?: boolean;
}

interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  attempts: number;
}

/**
 * Implements exponential backoff with jitter for handling rate limits
 * @param fn Function to execute with retry logic
 * @param options Retry configuration options
 */
async function executeWithExponentialBackoff<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    maxRetries = 10,
    baseDelayMs = 5000,
    maxDelayMs = 30000,
    jitterFactor = 0.3,
    retryOn429Only = true
  } = options;

  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await fn();
      if (attempt > 0) {
        console.log(`✅ Request succeeded after ${attempt} retries`);
      }
      return result;
    } catch (error: any) {
      lastError = error;
      
      // Check if we should retry based on error type
      const isRateLimitError = error?.status === 429 ||
                              error?.response?.status === 429 ||
                              error?.code === 429 ||
                              (error?.message && error.message.includes('429')) ||
                              (error?.innerMessage && error.innerMessage.includes('429'));
      
      const shouldRetry = retryOn429Only ? isRateLimitError : true;
      
      if (!shouldRetry || attempt === maxRetries) {
        console.error(`❌ Final attempt failed (${attempt + 1}/${maxRetries + 1}):`, {
          status: error?.status,
          code: error?.code,
          message: error?.message || error?.innerMessage,
          isRateLimitError
        });
        throw error;
      }

      // Calculate delay with exponential backoff and jitter
      const exponentialDelay = Math.min(baseDelayMs * Math.pow(2, attempt), maxDelayMs);
      const jitter = exponentialDelay * jitterFactor * Math.random();
      const delay = Math.floor(exponentialDelay + jitter);

      console.warn(`⚠️  Rate limit hit (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${delay}ms...`, {
        status: error?.status,
        code: error?.code,
        message: error?.message || error?.innerMessage,
        orderId: error?.orderId || 'unknown'
      });

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * Enhanced processAwsPromise with built-in rate limiting for 429 errors
 */
export async function processAwsPromiseWithRetry<T>(
  promiseFactory: () => Promise<T>,
  printOutput = false,
  retryOptions: RetryOptions = {}
): Promise<T> {
  return executeWithExponentialBackoff(async () => {
    try {
      const data = await promiseFactory();
      if ((data as any).data?.status === 'INVALID') {
        console.log(`STATUS INVALID: ${JSON.stringify((data as any).data)}`);
      }
      if (printOutput) {
        console.log(JSON.stringify((data as any).data, null, 2));
      }
      return data;
    } catch (e: any) {
      if (e.response?.data) {
        console.log('Error Data: ', JSON.stringify(e.response.data));
      } else {
        console.log(JSON.stringify(e, null, 2));
      }
      throw e;
    }
  }, retryOptions);
}


type RegionString = 'na' | 'eu' | 'fe';
export interface Marketplace {
  id: string;
  region: RegionString;
  language: string;
  sellerId?: string;
  requiredAttributes?: typeof requiredAttributesRegional["US"];
  processAttributes?: (attributes: Record<string, any>) => void;
  _extraAttributes?: Record<string, any>;
}

export const marketplaceIds: Record<string, Marketplace> = {
  US: {
    id: 'ATVPDKIKX0DER',
    region: 'na',
    language: 'en_US',
    sellerId: 'A2BYKIV8I1B6A8',
    requiredAttributes: requiredAttributesRegional.US,
    processAttributes: (attributes) => {
      delete attributes.cpsia_cautionary_statement;
      delete attributes.deprecated_offering_start_date;
    },
  },
  CA: {
    id: 'A2EUQ1WTGCTBG2',
    region: 'na',
    language: 'en_CA',
    requiredAttributes: requiredAttributesRegional.CA,
    processAttributes: (attributes) => {
      delete attributes.item_type_keyword;
    },
  },
  AU: {
    id: 'A39IBJ37TRP1C6',
    region: 'fe',
    language: 'en_AU',
    processAttributes: function (attributes) {
      delete attributes.item_type_keyword;
      delete attributes.cpsia_cautionary_statement;
      Object.assign(attributes, this._extraAttributes);
    },
    get _extraAttributes() {
      return {
        recommended_browse_nodes: [
          {
            marketplace_id: this.id,
            value: '6066129011',
          },
        ],
        power_plug_type: [
          {
            marketplace_id: this.id,
            value: 'no_plug',
          },
        ],
        fulfillment_availability: [
          {
            fulfillment_channel_code: 'AMAZON_JP',
            marketplace_id: this.id,
          },
        ],
      };
    },
  },
  UK: {
    id: 'A1F83G8C2ARO7P',
    region: 'eu',
    language: 'en_GB',
  },
};

type Region = {
  name: string;
  refresh_token: string | undefined;
};

export const regions: Record<RegionString, Region | string> = {
  na: {
    name: 'North America',
    refresh_token: naRefreshToken,
  },
  eu: 'Europe',
  fe: {
    name: 'Far East',
    refresh_token: feRefreshToken,
  },
};

export const reportTypes: Record<string, string> = {
  settlementReport: 'GET_V2_SETTLEMENT_REPORT_DATA_FLAT_FILE',
  restockReport: 'GET_RESTOCK_INVENTORY_RECOMMENDATIONS_REPORT',
};

export class spApiSdkAuthClass {
  #auth: SellingPartnerApiAuth | undefined;
  #region: RegionString;

  constructor(region: RegionString) {
    this.#region = region;
  }

  get auth(): SellingPartnerApiAuth {
    return this.#auth || this.getAuth();
  }

  private getAuth(): SellingPartnerApiAuth {
    if (!env.SELLING_PARTNER_APP_CLIENT_ID || !env.SELLING_PARTNER_APP_CLIENT_SECRET) {
      throw new Error('Missing AWS credentials');
    }
    const regionData = regions[this.#region];
    if (typeof regionData === 'string' || !regionData.refresh_token) {
      throw new Error(`Invalid region data for ${this.#region}`);
    }
    this.#auth = new SellingPartnerApiAuth({
      clientId: env.SELLING_PARTNER_APP_CLIENT_ID as string,
      clientSecret: env.SELLING_PARTNER_APP_CLIENT_SECRET as string,
      refreshToken: regionData.refresh_token,
    });
    return this.#auth;
  }

  fulfillmentOutboundClient = (): FulfillmentOutboundApiClient =>
    new FulfillmentOutboundApiClient({
      auth: this.auth,
      region: this.#region,
      rateLimiting: {
        retry: true,
        onRetry: (retryInfo) => {
          console.log('Retrying');
          console.log(
            `Code: ${retryInfo.error.code}, ${retryInfo.rateLimit}, ${retryInfo.error.response?.headers['x-amzn-RateLimit-Limit']}`
          );
        },
      },
    });
}

export async function processAwsPromise<T>(p: Promise<T>, printOutput = false): Promise<T> {
  try {
    const data = await p;
    if ((data as any).data?.status === 'INVALID') {
      console.log(`STATUS INVALID: ${JSON.stringify((data as any).data)}`);
    }
    if (printOutput) {
      console.log(JSON.stringify((data as any).data, null, 2));
    }
    return data;
  } catch (e: any) {
    if (e.response?.data) {
      console.log('Error Data: ', JSON.stringify(e.response.data));
    } else {
      console.log(JSON.stringify(e, null, 2));
    }
    throw e;
  }
}

export async function callAwsWithPagination<T>(
  f: (params: any) => Promise<T>,
  params: any,
  processResult: (result: T) => Promise<void>
): Promise<void> {
  let result;
  do {
    try {
      result = await processAwsPromise(f(params));
    } catch (e) {
      console.log(`Error callAwsWithPagination(${JSON.stringify(params)})`);
      throw e;
    }
    await processResult(result);
    params.nextToken = (result as any).data.nextToken || (result as any).data.payload?.nextToken || (result as any).data.payload?.NextToken || (result as any).data.pagination?.nextToken;
  } while (params.nextToken);
}

export class AmazonSeller {
  spApiSdkAuth: spApiSdkAuthClass;
  region: RegionString;
  defaultMarketplaceIds: string[];

  constructor(region: RegionString, defaultMarketplaceIds: string[]) {
    this.region = region;
    this.spApiSdkAuth = new spApiSdkAuthClass(region);
    this.defaultMarketplaceIds = defaultMarketplaceIds;
  }

  async getFBAInventory(marketplaceIds: string[], additionalParams: Record<string, any> = {}): Promise<any[]> {
    const client = new FbaInventoryApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
    });
    let params: any = {
      granularityType: 'Marketplace',
      granularityId: marketplaceIds[0],
      marketplaceIds: marketplaceIds,
      details: true,
      ...additionalParams,
    };
    let inventory: any[] = [];
    let result;

    do {
      result = await processAwsPromise(client.getInventorySummaries(params));
      inventory = [...inventory, ...result.data.payload.inventorySummaries];
      params.nextToken = result.data.pagination?.nextToken;
    } while (params.nextToken);
    return inventory;
  }

  async listAllFulfillmentOrders(queryStartDate: string): Promise<any[]> {
    const client = this.spApiSdkAuth.fulfillmentOutboundClient();
    let params: any = { queryStartDate: queryStartDate };
    let orders: any[] = [];
    let result;
    do {
      result = await processAwsPromise(client.listAllFulfillmentOrders(params));
      orders = [...orders, ...result.data.payload.fulfillmentOrders];
      params = { nextToken: result.data.payload.nextToken };
    } while (params.nextToken);
    return orders;
  }

  async getFulfillmentOrder(sellerFulfillmentOrderId: string) {
    const client = this.spApiSdkAuth.fulfillmentOutboundClient();
    return processAwsPromise(
      client.getFulfillmentOrder({ sellerFulfillmentOrderId: sellerFulfillmentOrderId })
    );
  }

  getCatalogItem(asin: string, marketplaceId: string) {
    const client = new CatalogItemsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
      rateLimiting: {
        retry: true,
      },
    });
    const params = {
      asin,
      marketplaceIds: [marketplaceId],
      includedData: ['attributes', 'identifiers', 'summaries', 'relationships', 'images'] as GetCatalogItemIncludedDataEnum[],
    };
    return processAwsPromise(client.getCatalogItem(params));
  }

  getListingItem(sku: string, marketplace: Marketplace) {
    const client = new ListingsItemsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
      rateLimiting: {
        retry: true,
      },
    });
    const params = {
      sellerId: marketplace.sellerId!,
      sku: sku,
      marketplaceIds: [marketplace.id],
      includedData: [
        'attributes',
        'issues',
        'summaries',
        'offers',
        'fulfillmentAvailability',
      ] as GetListingsItemIncludedDataEnum[],
    };
    return processAwsPromise(client.getListingsItem(params));
  }

  async deleteListingItem(sku: string, marketplace: Marketplace) {
    const client = new ListingsItemsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
    });
    const params = {
      sellerId: marketplace.sellerId!,
      sku: sku,
      marketplaceIds: [marketplace.id],
    };
    return processAwsPromise(client.deleteListingsItem(params));
  }

  updateListingParent(childSku: string, parentSku: string) {
    const client = new ListingsItemsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
    });
    return processAwsPromise(
      client.patchListingsItem({
        marketplaceIds: ['ATVPDKIKX0DER'],
        sellerId: 'A2BYKIV8I1B6A8',
        sku: childSku,
        body: {
          productType: 'THERMOPLASTIC_FILAMENT',
          patches: [
            {
              op: 'replace',
              path: '/attributes/parentage_level',
              value: [{ value: 'child', marketplace_id: 'ATVPDKIKX0DER' }],
            },
            {
              op: 'replace',
              path: '/attributes/child_parent_sku_relationship',
              value: [
                {
                  child_relationship_type: 'variation',
                  marketplaceId: 'ATVPDKIKX0DER',
                  parent_sku: parentSku,
                },
              ],
            },
            {
              op: 'replace',
              path: '/attributes/variation_theme',
              value: [{ name: 'COLOR', marketplaceId: 'ATVPDKIKX0DER' }],
            },
          ],
        },
      })
    );
  }

  createParentListing(sku: string, name: string, marketplaceId: string) {
    const client = new ListingsItemsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
    });
    return processAwsPromise(
      client.putListingsItem({
        marketplaceIds: [marketplaceId],
        sellerId: 'A2BYKIV8I1B6A8',
        sku: sku,
        body: {
          productType: 'THERMOPLASTIC_FILAMENT',
          attributes: parentListingAttributesTemplate(name, marketplaceId),
        },
      })
    );
  }

  async createListingFromFirestore(
    skuToCopy: string | null | undefined, // Make skuToCopy optional
    sku: string,
    attributeOverwrite: Record<string, any>, // This becomes the primary source if skuToCopy is null
    marketplace: Marketplace,
    patch: boolean,
    deleteImages: boolean // This might need re-evaluation if not copying
  ) {
    const client = new ListingsItemsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: marketplace.region,
    });

    let attributes: Record<string, any>;

    if (skuToCopy) {
      // Original logic: Fetch template and merge
      const listingToCopy = await firebase.get<AmazonListing>('amazon-listings', skuToCopy);
      if (!listingToCopy || !listingToCopy.listingDetails?.attributes) {
        // Handle case where listing or its attributes are missing
        console.warn(`Listing template ${skuToCopy} or its attributes not found in firestore. Using only provided attributes.`);
        attributes = { ...attributeOverwrite }; // Use only provided attributes as fallback
      } else {
        let attributesToCopy = { ...listingToCopy.listingDetails.attributes }; // Clone to avoid modifying cache

        delete attributesToCopy['is_expiration_dated_product'];

        if (deleteImages) {
          // This logic might be less relevant if not copying, but keep for now
          Object.keys(attributesToCopy).forEach((key) => {
            if (key.includes('_image_locator')) {
              delete attributesToCopy[key];
            }
          });
        }
        // Merge provided attributes onto the copied template
        attributes = deepMergeEmpty(attributeOverwrite, attributesToCopy);
      }
    } else {
      // New logic: Use attributeOverwrite directly as the source
      console.log(`Creating/updating listing for ${sku} using only provided attributes.`);
      attributes = { ...attributeOverwrite }; // Use a clone of the provided attributes
    }


    // Common processing logic for attributes
    marketplace.processAttributes?.(attributes);

    // Ensure marketplace IDs and language tags are correct
    Object.keys(attributes).forEach((key) => {
      // Ensure the value is an array before iterating
      if (Array.isArray(attributes[key])) {
        for (let i = 0; i < attributes[key].length; i++) {
          // Check if the item is an object before accessing properties
          if (typeof attributes[key][i] === 'object' && attributes[key][i] !== null) {
        if (attributes[key][i].marketplace_id) {
          attributes[key][i].marketplace_id = marketplace.id;
        }
            if (attributes[key][i].marketplace_id) {
              attributes[key][i].marketplace_id = marketplace.id;
            }
            if (attributes[key][i].marketplaceId) { // Handle potential alternative casing
              attributes[key][i].marketplaceId = marketplace.id;
            }
            if (attributes[key][i].language_tag) {
              attributes[key][i].language_tag = marketplace.language;
            }
          }
        }
      } else {
         // Handle cases where attribute value might not be an array if necessary
         // console.warn(`Attribute ${key} for SKU ${sku} is not an array:`, attributes[key]);
      }
    });

    console.log("CreateListingFromFirestore attributes:")
    console.log(JSON.stringify(attributes, null, 2));

    if (patch) {
      const patchParams = {
        marketplaceIds: [marketplace.id],
        sellerId: marketplace.sellerId!,
        sku: sku,
        body: {
          productType: 'THERMOPLASTIC_FILAMENT',
          patches: Object.entries(attributes).map(([key, value]) => ({
            op: 'replace' as PatchOperationOpEnum,
            path: `/attributes/${key}`,
            value: value as Record<string, any>[],
          })),
        },
      }
      console.log("patchParams", JSON.stringify(patchParams, null, 2));
      return processAwsPromise( client.patchListingsItem(patchParams), true );
    }
    else {
      return processAwsPromise(
          client.putListingsItem({
              marketplaceIds: [marketplace.id],
              sellerId: marketplace.sellerId!,
              sku: sku,
              body: {
                productType: 'THERMOPLASTIC_FILAMENT',
                attributes: attributes,
              },
            }),
        true
      );
  
    }
    
  }

  getProductTypeDefinition(productType: string, marketplace: Marketplace) {
    const client = new ProductTypeDefinitionsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
      rateLimiting: {
        retry: true,
      },
    });
    const params = {
      productType: productType,
      sellerId: marketplace.sellerId!,
      marketplaceIds: [marketplace.id],
    };
    return processAwsPromise(client.getDefinitionsProductType(params));
  }

  async createReport(reportType: string) {
    const client = new ReportsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
      rateLimiting: { retry: true },
    });
    const params = { reportType, marketplaceIds: this.defaultMarketplaceIds };
    const result = await processAwsPromise(client.createReport({ body: params }));
    return result;
  }

  async getReportsByType(reportType: string): Promise<any[]> {
    const client = new ReportsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
      rateLimiting: {
        retry: true,
      },
    });
    const params = {
      reportTypes: [reportType],
      pageSize: 100,
    };

    let results: any[] = [];
    await callAwsWithPagination(client.getReports.bind(client), params, async (result) => {
      results = [...results, ...result.data.reports];
    });
    console.log(JSON.stringify(results, null, 2));
    console.log(`Found ${results.length} reports`);
    return results;
  }

  async saveReportsByType(reportType: string): Promise<void> {
    const client = new ReportsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
      rateLimiting: {
        retry: true,
      },
    });
    const reports = await this.getReportsByType(reportType);
    if (reports.length === 0) {
      console.log(`No reports found for report type: ${reportType}`);
      return;
    }
    const report = reports[0];
    console.log(`Saving report ${report}`);

    const reportDocument = await amazonSpApi.getReportDocument(report.reportDocumentId);
    if (!reportDocument) {
      console.log(`No report document found for report ID: ${report.reportId}`);
      return;
    }
    const reportDocumentUrl = reportDocument.url;

    let response = await fetch(reportDocumentUrl);
    const responseText = await response.text();
    fs.writeFileSync(path.join(__dirname, `/output/amazon/${report.reportId}.txt`), responseText);
  }

  // Implement getItemGuidance when SP-API SDK supports it
  // async getItemGuidance(sellerSkus: string[]): Promise<any[]> {
  //   // Implementation using SP-API SDK
  // }

  async getAdvertisingPayments(): Promise<void> {
    let response = await amazonSpApi.getFinancialEventGroups();
    console.log(inspect(response));
  }

  async updateProductImages(sku: string, imageUrls: string[]) {
    const client = new ListingsItemsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region as SellingPartnerRegion,
      rateLimiting: {
        retry: true,
      },
    });

    const patches = imageUrls.map((url, index) => ({
      op: 'replace' as PatchOperationOpEnum,
      path: `/attributes/main_product_image_locator${index === 0 ? '' : index + 1}`,
      value: [{ media_location: url, marketplace_id: this.defaultMarketplaceIds[0] }],
    }));
    console.log("Update product images", JSON.stringify(patches, null, 2));

    return processAwsPromise(
      client.patchListingsItem({
        marketplaceIds: this.defaultMarketplaceIds,
        sellerId: marketplaceIds.US.sellerId!,
        sku: sku,
        body: {
          productType: 'THERMOPLASTIC_FILAMENT',
          patches: patches,
        },
      })
    );
  }

  /**
   * Fetch Amazon orders using the SP-API Orders endpoint
   */
  async getOrders(params: {
    createdAfter?: string;
    createdBefore?: string;
    lastUpdatedAfter?: string;
    lastUpdatedBefore?: string;
    orderStatuses?: string[];
    marketplaceIds?: string[];
    fulfillmentChannels?: string[];
    paymentMethods?: string[];
    buyerEmail?: string;
    sellerOrderId?: string;
    maxResultsPerPage?: number;
    easyShipShipmentStatuses?: string[];
    electronicInvoiceStatuses?: string[];
    nextToken?: string;
  }): Promise<AmazonOrder[]> {
    console.log('Fetching Amazon orders from SP-API...');
    
    const client = new OrdersApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region as any,
      rateLimiting: {
        retry: true,
        onRetry: (retryInfo) => {
          console.log("Retrying orders request");
          console.log(`Code: ${retryInfo.error.code}, ${retryInfo.rateLimit}`);
        },
      },
    });

    const requestParams = {
      marketplaceIds: params.marketplaceIds || this.defaultMarketplaceIds,
      createdAfter: params.createdAfter,
      createdBefore: params.createdBefore,
      lastUpdatedAfter: params.lastUpdatedAfter,
      lastUpdatedBefore: params.lastUpdatedBefore,
      orderStatuses: params.orderStatuses,
      fulfillmentChannels: params.fulfillmentChannels,
      paymentMethods: params.paymentMethods,
      buyerEmail: params.buyerEmail,
      sellerOrderId: params.sellerOrderId,
      maxResultsPerPage: params.maxResultsPerPage || 100,
      easyShipShipmentStatuses: params.easyShipShipmentStatuses,
      electronicInvoiceStatuses: params.electronicInvoiceStatuses,
      nextToken: params.nextToken,
    };

    // Remove undefined values
    Object.keys(requestParams).forEach(key => {
      if (requestParams[key as keyof typeof requestParams] === undefined) {
        delete requestParams[key as keyof typeof requestParams];
      }
    });

    try {
      const result = await processAwsPromiseWithRetry<any>(
        () => client.getOrders(requestParams),
        false,
        {
          maxRetries: 8,
          baseDelayMs: 3000,
          maxDelayMs: 90000,
          jitterFactor: 0.3,
          retryOn429Only: true
        }
      );
      return result.data.payload?.Orders || [];
    } catch (error) {
      console.error('Error fetching Amazon orders:', error);
      throw new Error(`Failed to fetch Amazon orders: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Fetch Amazon order items for a specific order with robust rate limiting
   */
  async getOrderItems(orderId: string): Promise<AmazonOrderItem[]> {
    console.log(`Fetching order items for order: ${orderId}`);
    
    const client = new OrdersApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region as any,
      rateLimiting: {
        retry: true,
        onRetry: (retryInfo) => {
          console.log("SDK built-in retry for order items request");
          console.log(`Code: ${retryInfo.error.code}, ${retryInfo.rateLimit}`);
        },
      },
    });

    try {
      const result = await processAwsPromiseWithRetry<any>(
        () => client.getOrderItems({ orderId }),
        false,
        {
          maxRetries: 10,
          baseDelayMs: 5000,
          maxDelayMs: 120000,
          jitterFactor: 0.3,
          retryOn429Only: true
        }
      );
      
      const orderItems = result.data.payload?.OrderItems || [];
      
      // Add the orderId to each item for consistency
      return orderItems.map((item: any) => ({
        ...item,
        AmazonOrderId: orderId
      }));
    } catch (error: any) {
      // Add orderId to error context for better debugging
      if (error && typeof error === 'object') {
        error.orderId = orderId;
      }
      console.error(`Error fetching order items for order ${orderId}:`, error);
      throw new Error(`Failed to fetch order items: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Get report status using the SP-API Reports endpoint
   */
  async getReportStatus(reportId: string): Promise<ReportStatus | null> {
    console.log(`Getting status for report: ${reportId}`);
    
    const client = new ReportsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
      rateLimiting: {
        retry: true,
        onRetry: (retryInfo) => {
          console.log("Retrying get report status request");
          console.log(`Code: ${retryInfo.error.code}, ${retryInfo.rateLimit}`);
        },
      },
    });

    try {
      const result = await processAwsPromise<any>(client.getReport({ reportId }));
      return result.data || null;
    } catch (error) {
      console.error(`Error getting report status for ${reportId}:`, error);
      return null;
    }
  }

  /**
   * Download report content using the SP-API Reports endpoint
   */
  async downloadReportContent(reportDocumentId: string): Promise<string | null> {
    console.log(`Downloading report content for document: ${reportDocumentId}`);
    
    const client = new ReportsApiClient({
      auth: this.spApiSdkAuth.auth,
      region: this.region,
      rateLimiting: {
        retry: true,
        onRetry: (retryInfo) => {
          console.log("Retrying download report request");
          console.log(`Code: ${retryInfo.error.code}, ${retryInfo.rateLimit}`);
        },
      },
    });

    try {
      // First get the report document URL with rate limiting
      const documentResult = await processAwsPromiseWithRetry<any>(
        () => client.getReportDocument({ reportDocumentId }),
        false,
        {
          maxRetries: 3,
          baseDelayMs: 1000,
          maxDelayMs: 15000,
          jitterFactor: 0.3,
          retryOn429Only: true
        }
      );
      const documentUrl = documentResult.data?.url;
      
      if (!documentUrl) {
        console.error('No document URL found in report document response');
        return null;
      }

      // Download the actual content from the URL
      const response = await fetch(documentUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const content = await response.text();
      console.log(`Successfully downloaded report content (${content.length} characters)`);
      return content;
    } catch (error) {
      console.error(`Error downloading report content for ${reportDocumentId}:`, error);
      return null;
    }
  }

  /**
   * Request settlement reports using the SP-API Reports endpoint
   */
  async requestSettlementReports(marketplaceIds?: string[]): Promise<ReportStatus[]> {
    console.log('Requesting settlement reports...');
    
    const reportTypes = [
      'GET_V2_SETTLEMENT_REPORT_DATA_FLAT_FILE',
      'GET_AMAZON_PAYMENTS_REPORTS_PAYMENT_TRANSACTION_DETAILS_REPORT'
    ];

    const results: ReportStatus[] = [];

    for (const reportType of reportTypes) {
      try {
        console.log(`Creating report of type: ${reportType}`);
        const reportRequest = await this.createReport(reportType);
        
        if (reportRequest?.data?.reportId) {
          results.push({
            reportId: reportRequest.data.reportId,
            reportType: reportType,
            processingStatus: reportRequest.data.processingStatus || 'IN_QUEUE',
            marketplaceIds: marketplaceIds || this.defaultMarketplaceIds,
            createdTime: reportRequest.data.createdTime
          });
        }
      } catch (error) {
        console.error(`Error requesting ${reportType} report:`, error);
      }
    }

    console.log(`Successfully requested ${results.length} settlement reports`);
    return results;
  }

  /**
   * Request Amazon Pay settlement reports using the SP-API Reports endpoint
   */
  async requestAmazonPaySettlementReports(marketplaceIds?: string[]): Promise<ReportStatus[]> {
    console.log('Requesting Amazon Pay settlement reports...');
    
    const reportType = 'GET_AMAZON_PAYMENTS_REPORTS_PAYMENT_TRANSACTION_DETAILS_REPORT';
    const results: ReportStatus[] = [];

    try {
      console.log(`Creating report of type: ${reportType}`);
      const reportRequest = await this.createReport(reportType);
      
      if (reportRequest?.data?.reportId) {
        results.push({
          reportId: reportRequest.data.reportId,
          reportType: reportType,
          processingStatus: reportRequest.data.processingStatus || 'IN_QUEUE',
          marketplaceIds: marketplaceIds || this.defaultMarketplaceIds,
          createdTime: reportRequest.data.createdTime
        });
      }
    } catch (error) {
      console.error(`Error requesting ${reportType} report:`, error);
    }

    console.log(`Successfully requested ${results.length} Amazon Pay settlement reports`);
    return results;
  }

  /**
   * Get settlement reports and their content
   */
  async getSettlementReports(): Promise<SettlementRecord[]> {
    console.log('Fetching settlement reports...');
    
    const reportTypes = [
      'GET_V2_SETTLEMENT_REPORT_DATA_FLAT_FILE',
      'GET_AMAZON_PAYMENTS_REPORTS_PAYMENT_TRANSACTION_DETAILS_REPORT'
    ];

    const settlements: SettlementRecord[] = [];

    for (const reportType of reportTypes) {
      try {
        const reports = await this.getReportsByType(reportType);
        
        for (const report of reports) {
          if (report.processingStatus === 'DONE' && report.reportDocumentId) {
            const content = await this.downloadReportContent(report.reportDocumentId);
            if (content) {
              const parsedSettlement = await this.parseSettlementReportContent(content, reportType);
              if (parsedSettlement) {
                settlements.push(parsedSettlement);
              }
            }
          }
        }
      } catch (error) {
        console.error(`Error processing ${reportType} reports:`, error);
      }
    }

    console.log(`Retrieved ${settlements.length} settlement records`);
    return settlements;
  }

  /**
   * Parse settlement report content (adapts logic from amazon-helper.mjs)
   */
  private async parseSettlementReportContent(content: string, reportType: string): Promise<SettlementRecord | null> {
    try {
      if (reportType === 'GET_V2_SETTLEMENT_REPORT_DATA_FLAT_FILE') {
        // Parse tab-delimited settlement report
        const lines = content.split('\n').filter(line => line.trim());
        if (lines.length === 0) return null;

        const headers = lines[0].split('\t');
        const data: any[] = [];

        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split('\t');
          const row: any = {};
          headers.forEach((header, index) => {
            row[header] = values[index] || '';
          });
          data.push(row);
        }

        if (data.length === 0) return null;

        const settlementFields = [
          "settlement-id",
          "settlement-start-date",
          "settlement-end-date",
          "deposit-date",
          "total-amount",
          "currency",
        ];

        let settlement: any = {};
        settlementFields.forEach((field) => {
          settlement[field] = data[0][field];
        });

        // Remove the settlement fields from the transactions
        settlement.transactions = data.slice(1).map((item) => {
          let newItem: any = {};
          Object.keys(item).forEach((key) => {
            if (!settlementFields.includes(key)) {
              newItem[key] = item[key];
            }
          });
          return newItem;
        });

        // Add the total amount of the price-amount where the price-type is Principal
        settlement.totalPrincipal = settlement.transactions.reduce((acc: number, curr: any) => {
          if (curr["price-type"] === "Principal") {
            return acc + parseFloat(curr["price-amount"] || "0");
          } else {
            return acc;
          }
        }, 0);

        return settlement as SettlementRecord;
      }
      
      return null;
    } catch (error) {
      console.error('Error parsing settlement report content:', error);
      return null;
    }
  }

  /**
   * Get Amazon Pay settlement reports
   */
  async getAmazonPaySettlementReports(): Promise<AmazonPaySettlementRecord[]> {
    console.log('Fetching Amazon Pay settlement reports...');
    
    const reportType = 'GET_AMAZON_PAYMENTS_REPORTS_PAYMENT_TRANSACTION_DETAILS_REPORT';
    const settlements: AmazonPaySettlementRecord[] = [];

    try {
      const reports = await this.getReportsByType(reportType);
      
      for (const report of reports) {
        if (report.processingStatus === 'DONE' && report.reportDocumentId) {
          const content = await this.downloadReportContent(report.reportDocumentId);
          if (content) {
            const parsedSettlement = await this.parseAmazonPaySettlementReportContent(content);
            if (parsedSettlement) {
              settlements.push(parsedSettlement);
            }
          }
        }
      }
    } catch (error) {
      console.error(`Error processing Amazon Pay settlement reports:`, error);
    }

    console.log(`Retrieved ${settlements.length} Amazon Pay settlement records`);
    return settlements;
  }

  /**
   * Parse Amazon Pay settlement report content (adapts logic from amazon-helper.mjs parseAmazonPaySettlementReport)
   */
  private async parseAmazonPaySettlementReportContent(content: string): Promise<AmazonPaySettlementRecord | null> {
    try {
      const structure: any = {};
      const fileContent = content.split('\n');

      // Process header content in lines 2-4 (0-indexed)
      const headerContent = fileContent.slice(2, 5);
      for (let line of headerContent) {
        const [key, value] = line.split(',');
        if (value) {
          structure[key] = value;
        }
      }

      // Parse CSV content starting from line 5 (skip first 5 lines)
      structure.transactions = await this.parseCsvContent(content, { separator: ',', skipLines: 5 });
      
      // Calculate total NetTransactionAmount
      structure.NetTransactionAmountTotal = structure.transactions.reduce((acc: number, curr: any) => {
        return acc + parseFloat(curr.NetTransactionAmount || '0');
      }, 0);
      
      // Get settlement-id from first transaction's SettlementId
      if (structure.transactions.length > 0) {
        structure['settlement-id'] = structure.transactions[0]['SettlementId'];
      }

      return structure as AmazonPaySettlementRecord;
    } catch (error) {
      console.error('Error parsing Amazon Pay settlement report content:', error);
      return null;
    }
  }

  /**
   * Parse CSV content from string (similar to readCsvFile from csvHelper.mjs)
   */
  private async parseCsvContent(content: string, options: { separator?: string; skipLines?: number } = {}): Promise<any[]> {
    const { separator = ',', skipLines = 0 } = options;
    
    try {
      const lines = content.split('\n').filter(line => line.trim());
      
      if (lines.length <= skipLines) {
        return [];
      }

      // Skip specified lines and get header row
      const relevantLines = lines.slice(skipLines);
      if (relevantLines.length === 0) {
        return [];
      }

      const headers = relevantLines[0].split(separator);
      const data: any[] = [];

      // Parse data rows
      for (let i = 1; i < relevantLines.length; i++) {
        const values = relevantLines[i].split(separator);
        const row: any = {};
        
        headers.forEach((header, index) => {
          row[header.trim()] = values[index] ? values[index].trim() : '';
        });
        
        data.push(row);
      }

      return data;
    } catch (error) {
      console.error('Error parsing CSV content:', error);
      return [];
    }
  }

  /**
   * Get FBA inventory for a specific marketplace (helper method for compatibility)
   */
  async getFbaInventoryForMarketplace(marketplaceId: string): Promise<any[]> {
    console.log(`Fetching FBA inventory for marketplace: ${marketplaceId}`);
    
    return this.getFBAInventory([marketplaceId], {
      granularityType: 'Marketplace',
      granularityId: marketplaceId,
    });
  }

  /**
   * Get FBA inventory for a specific SKU (helper method for compatibility)
   */
  async getFbaInventoryForSku(sku: string, marketplaceId?: string): Promise<any[]> {
    console.log(`Fetching FBA inventory for SKU: ${sku}`);
    
    const marketplaceIds = marketplaceId ? [marketplaceId] : this.defaultMarketplaceIds;
    
    return this.getFBAInventory(marketplaceIds, {
      sellerSkus: [sku],
      granularityType: 'Marketplace',
      granularityId: marketplaceIds[0],
    });
  }

  /**
   * Get MCF orders (helper method for compatibility)
   */
  async getMcfOrders(queryStartDate?: string): Promise<any[]> {
    console.log('Fetching MCF orders from SP-API...');
    
    if (queryStartDate) {
      return this.listAllFulfillmentOrders(queryStartDate);
    } else {
      // Default to a reasonable start date if none provided
      return this.listAllFulfillmentOrders('2020-01-01');
    }
  }

  /**
   * Get MCF order details for a specific order (helper method for compatibility)
   */
  async getMcfOrderDetails(sellerFulfillmentOrderId: string): Promise<any> {
    console.log(`Fetching MCF order details for: ${sellerFulfillmentOrderId}`);
    
    return this.getFulfillmentOrder(sellerFulfillmentOrderId);
  }

}
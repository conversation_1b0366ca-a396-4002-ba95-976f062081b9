import firebase from '~/data/google/firebase-server';
import {
  CreateFacebookConversationData,
  CreateFacebookMessageData,
  CreateDownloadMetadataData,
  FacebookConversation,
  FacebookMessage,
  DownloadMetadata,
  ReadFacebookConversation,
  ReadFacebookMessage,
  UpdateFacebookConversationData,
  UpdateFacebookMessageData,
  UpdateDownloadMetadataData,
} from './types';
import { WhereFilterOp } from 'firebase/firestore';

export class FacebookMessageError extends Error {
  constructor(message: string, public code: string, public originalError?: Error) {
    super(message);
    this.name = 'FacebookMessageError';
  }
}

export class FacebookMessageRepository {
  private readonly conversationsCollection = 'facebook-conversations';
  private readonly downloadMetadataDoc = 'facebook-download-metadata';
  private readonly messagesSubcollection = 'messages';

  async createConversation(data: CreateFacebookConversationData): Promise<string> {
    try {
      await firebase.set(this.conversationsCollection, data.id, {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      return data.id;
    } catch (error) {
      throw new FacebookMessageError(
        'Failed to create conversation',
        'CREATE_ERROR',
        error as Error
      );
    }
  }

  async createMessage(conversationId: string, data: CreateFacebookMessageData): Promise<string> {
    try {
      await firebase.setSubData(
        this.conversationsCollection,
        this.messagesSubcollection,
        conversationId,
        {
          ...data,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        data.id
      );
      return data.id;
    } catch (error) {
      throw new FacebookMessageError(
        'Failed to create message',
        'CREATE_ERROR',
        error as Error
      );
    }
  }

  async batchCreateConversations(conversations: CreateFacebookConversationData[]): Promise<string[]> {
    try {
      const data = conversations.map(c => ({
        ...c,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));
      await firebase.batchSet(this.conversationsCollection, data);
      return conversations.map(c => c.id);
    } catch (error) {
      throw new FacebookMessageError(
        'Failed to batch create conversations',
        'BATCH_CREATE_ERROR',
        error as Error
      );
    }
  }

  async batchCreateMessages(conversationId: string, messages: CreateFacebookMessageData[]): Promise<string[]> {
    try {
      const promises = messages.map(message => 
        this.createMessage(conversationId, message)
      );
      return await Promise.all(promises);
    } catch (error) {
      throw new FacebookMessageError(
        'Failed to batch create messages',
        'BATCH_CREATE_ERROR',
        error as Error
      );
    }
  }

  async getConversation(id: string): Promise<ReadFacebookConversation | null> {
    try {
      return await firebase.get(this.conversationsCollection, id);
    } catch (error) {
      throw new FacebookMessageError(
        `Failed to get conversation: ${id}`,
        'GET_ERROR',
        error as Error
      );
    }
  }

  async getMessage(conversationId: string, messageId: string): Promise<ReadFacebookMessage | null> {
    try {
      const messages = await firebase.getAll<ReadFacebookMessage>(
        this.conversationsCollection,
        conversationId,
        this.messagesSubcollection
      );
      return messages.find(m => m.id === messageId) || null;
    } catch (error) {
      throw new FacebookMessageError(
        `Failed to get message: ${messageId}`,
        'GET_ERROR',
        error as Error
      );
    }
  }

  async getConversationMessages(conversationId: string): Promise<ReadFacebookMessage[]> {
    try {
      return await firebase.getAll<ReadFacebookMessage>(
        this.conversationsCollection,
        conversationId,
        this.messagesSubcollection
      );
    } catch (error) {
      throw new FacebookMessageError(
        `Failed to get conversation messages: ${conversationId}`,
        'GET_ERROR',
        error as Error
      );
    }
  }

  async getPaginatedConversations(
    limit: number = 100,
    lastDoc?: ReadFacebookConversation | null,
    beforeDate?: Date
  ): Promise<{
    conversations: ReadFacebookConversation[];
    hasMore: boolean;
  }> {
    try {
      let queryConditions: [string, WhereFilterOp, any][] = [];

      if (beforeDate) {
        queryConditions.push(['updatedTime', '<', beforeDate]);
      }

      // Get all conversations that match the date filter
      const result = await firebase.queryCollection<ReadFacebookConversation>(
        this.conversationsCollection,
        queryConditions
      );

      // Sort by updatedTime in descending order
      const sortedConversations = result.sort((a, b) => {
        return b.updatedTime.getTime() - a.updatedTime.getTime();
      });

      // Apply pagination
      let startIndex = 0;
      if (lastDoc) {
        const lastIndex = sortedConversations.findIndex(c => c.id === lastDoc.id);
        if (lastIndex !== -1) {
          startIndex = lastIndex + 1;
        }
      }

      const paginatedConversations = sortedConversations.slice(startIndex, startIndex + limit);
      const hasMore = startIndex + limit < sortedConversations.length;

      return {
        conversations: paginatedConversations,
        hasMore
      };
    } catch (error) {
      throw new FacebookMessageError(
        'Failed to get paginated conversations',
        'GET_ERROR',
        error as Error
      );
    }
  }

  async updateConversation(id: string, data: UpdateFacebookConversationData): Promise<void> {
    try {
      await firebase.update(this.conversationsCollection, id, {
        ...data,
        updatedAt: new Date(),
      });
    } catch (error) {
      throw new FacebookMessageError(
        `Failed to update conversation: ${id}`,
        'UPDATE_ERROR',
        error as Error
      );
    }
  }

  async updateMessage(
    conversationId: string,
    messageId: string,
    data: UpdateFacebookMessageData
  ): Promise<void> {
    try {
      await firebase.updateSubcollection(
        this.conversationsCollection,
        conversationId,
        this.messagesSubcollection,
        messageId,
        {
          ...data,
          updatedAt: new Date(),
        }
      );
    } catch (error) {
      throw new FacebookMessageError(
        `Failed to update message: ${messageId}`,
        'UPDATE_ERROR',
        error as Error
      );
    }
  }

  async getDownloadMetadata(): Promise<DownloadMetadata | null> {
    try {
      return await firebase.get('metadata', this.downloadMetadataDoc);
    } catch (error) {
      throw new FacebookMessageError(
        'Failed to get download metadata',
        'GET_METADATA_ERROR',
        error as Error
      );
    }
  }

  async updateDownloadMetadata(data: UpdateDownloadMetadataData): Promise<void> {
    try {
      await firebase.update('metadata', this.downloadMetadataDoc, {
        ...data,
        updatedAt: new Date(),
      });
    } catch (error) {
      throw new FacebookMessageError(
        'Failed to update download metadata',
        'UPDATE_METADATA_ERROR',
        error as Error
      );
    }
  }

  async createDownloadMetadata(data: CreateDownloadMetadataData): Promise<void> {
    try {
      await firebase.set('metadata', this.downloadMetadataDoc, {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    } catch (error) {
      throw new FacebookMessageError(
        'Failed to create download metadata',
        'CREATE_METADATA_ERROR',
        error as Error
      );
    }
  }
}

export const facebookMessageRepository = new FacebookMessageRepository();

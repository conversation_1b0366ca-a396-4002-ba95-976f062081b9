{"name": "app-next", "version": "0.1.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "dotenvx run -f .env.encrypted -- next dev", "prebuild": "dotenvx run -f .env.encrypted -- tsx ./src/env", "build": "dotenvx run -f .env.encrypted -- next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "ts-run": "ts-node ./src/app/ts-run.ts", "tsx": "npx dotenvx run -f .env.encrypted -- tsx --inspect", "graphql-codegen": "graphql-codegen", "validate-env": "npx dotenvx run -f .env.encrypted -- tsx ./src/env.ts", "vercel:dev": "dotenvx run -f .env.encrypted -- npx vercel dev --cwd ../", "dotenv:encrypt": "npx dotenvx encrypt -f .env.encrypted", "dotenv:decrypt": "npx dotenvx decrypt -f .env.encrypted", "tunnel": "sudo tailscale serve -https 3001 3000"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.788.0", "@aws-sdk/client-s3": "^3.664.0", "@aws-sdk/client-ses": "^3.664.0", "@aws-sdk/client-textract": "^3.664.0", "@aws-sdk/lib-dynamodb": "^3.789.0", "@firebase/firestore": "^4.7.4", "@google-cloud/bigquery": "^7.9.1", "@google-cloud/firestore": "^6.8.0", "@google/generative-ai": "^0.19.0", "@heroicons/react": "^2.1.5", "@netlify/functions": "^2.8.2", "@nextui-org/react": "^2.4.8", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@scaleleap/amazon-marketplaces": "^18.0.1", "@scaleleap/selling-partner-api-sdk": "^7.0.0", "@scribelabsai/amazon-trp": "^1.0.0", "@shopify/admin-api-client": "^1.0.8", "@shopify/api-codegen-preset": "^1.1.7", "@shopify/shopify-api": "^11.12.0", "@sp-api-sdk/auth": "^2.0.13", "@sp-api-sdk/catalog-items-api-2022-04-01": "^2.4.1", "@sp-api-sdk/common": "^2.1.1", "@sp-api-sdk/fba-inventory-api-v1": "^2.4.0", "@sp-api-sdk/fulfillment-inbound-api-v0": "^2.5.1", "@sp-api-sdk/fulfillment-outbound-api-2020-07-01": "^2.6.0", "@sp-api-sdk/listings-items-api-2021-08-01": "^2.5.0", "@sp-api-sdk/orders-api-v0": "^3.5.0", "@sp-api-sdk/product-type-definitions-api-2020-09-01": "^2.4.2", "@sp-api-sdk/reports-api-2021-06-30": "^2.2.2", "@types/bwip-js": "^3.2.3", "@types/lodash": "^4.17.10", "@uiw/react-json-view": "2.0.0-alpha.12", "ag-grid-community": "^31.3.4", "ag-grid-react": "^31.3.4", "amazon-sp-api": "^0.8.4", "auth0": "^4.10.0", "aws-crt": "^1.22.0", "axios": "^1.7.7", "bwip-js": "^4.5.1", "class-variance-authority": "^0.7.0", "client-only": "^0.0.1", "clsx": "^2.1.1", "cookiecad-lib": "workspace:*", "cookiecad-shared": "workspace:^", "crisp-api": "^9.7.0", "csv-parse": "^5.5.6", "csv-parser": "^3.0.0", "csv-stringify": "^6.5.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "fast-xml-parser": "^4.5.0", "firebase": "^10.14.0", "firebase-admin": "^12.6.0", "firebase-functions": "^5.1.1", "firebaseui": "^6.1.0", "framer-motion": "^11.11.1", "jose": "^5.9.6", "json2csv": "^5.0.7", "lite": "link:@firebase/firestore/lite", "lodash": "^4.17.21", "lucide-react": "^0.447.0", "moment": "^2.29.4", "next": "^14.2.15", "next-firebase-auth-edge": "^1.9.1", "nodemailer": "^6.9.15", "openai": "^4.67.1", "p-throttle": "^6.2.0", "path": "^0.12.7", "pdf-lib": "^1.17.1", "pdfmake": "^0.2.13", "postgres": "^3.4.4", "rate-limiter-flexible": "^5.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-select": "^5.10.0", "shopify-api-node": "^3.15.0", "sonner": "^1.7.0", "stripe": "^10.17.0", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.1", "typesaurus": "^10.7.0", "xero-node": "^5.1.0", "xml2js": "^0.6.2", "zod": "^3.23.8"}, "devDependencies": {"@dotenvx/dotenvx": "^1.35.0", "@types/json2csv": "^5.0.7", "@types/node": "^22.7.4", "@types/nodemailer": "^6.4.16", "@types/pdfmake": "^0.2.9", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.20", "aws-cdk-lib": "2.110.1", "constructs": "10.3.0", "drizzle-kit": "^0.24.2", "drizzle-orm": "^0.33.0", "eslint": "^8.57.1", "eslint-config-next": "^14.2.15", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "ts-node": "^10.9.2", "typescript": "^5.6.2"}}